import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/smartAdDescription.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/app_const.dart' as appConst;



class AdDescriptionController extends GetxController {
  //// SMART DESCRIPTIONS TABLE
  bool loadingDescriptions = false;
  List<SmartAdDescription> descriptions = [];
  // filter 
  List<Map> filters = [];
  TextEditingController searchTextController = new TextEditingController();

  //// INSIDE VIEW
  bool waitingForGeneration = false;
  SmartAdDescription smartAdDescription = SmartAdDescription.empty();
  BaseAddressInfo? addressInfo;

  TextEditingController roomsController = new TextEditingController();
  TextEditingController bathroomsController = new TextEditingController();
  TextEditingController floorController = new TextEditingController();
  TextEditingController buildingFloorCountController = new TextEditingController();
  TextEditingController squareFootageController = new TextEditingController();
  TextEditingController descriptionController = new TextEditingController();
  TextEditingController propertyTypeController = new TextEditingController();
  TextEditingController usageTypeController = new TextEditingController();
  TextEditingController contractTypeController = new TextEditingController();
  TextEditingController maintenanceStatusController = new TextEditingController();
  TextEditingController energyClassController = new TextEditingController();
  TextEditingController additionalInfoController = new TextEditingController();
  TextEditingController acquirerTypeController = new TextEditingController();
  Map<String, dynamic> characteristicsMap = appConst.houseFeatures;

  TextEditingController descriptionLanguageController = new TextEditingController();
  double descriptionLength = 500;


  void clearFilter(){
    filters.clear();
    searchTextController.clear();
  }

  void clearController() {
    filters.clear();
    searchTextController.clear();
    descriptions.clear();
    loadingDescriptions = false;
  }

  void setInsideViewControllers(SmartAdDescription description) {
    roomsController.text = description.rooms?.toString() ?? "";
    roomsController.text = description.rooms == 5 ? "5+" : roomsController.text;
    bathroomsController.text = description.numberOfBathrooms?.toString() ?? "";
    bathroomsController.text = description.numberOfBathrooms == 3 ? "3+" : bathroomsController.text;
    floorController.text = description.unitFloor ?? "";
    buildingFloorCountController.text = description.buildingFloorCount ?? "";
    squareFootageController.text = description.grossSquareFootage?.toString() ?? "";
    descriptionController.text = description.generation.generatedDescription ?? "";
    propertyTypeController.text = description.propertyType ?? "";
    usageTypeController.text = description.usageType ?? "";
    contractTypeController.text = description.contractType ?? "";
    maintenanceStatusController.text = description.maintenanceStatus ?? "";
    energyClassController.text = description.energyClass ?? "";
    additionalInfoController.text = description.additionalInfo ?? "";
    acquirerTypeController.text = description.acquirerType ?? "";
    descriptionLanguageController.text = description.generation.generationLanguage ?? "";
    descriptionLength = description.generation.generationCharactersLenght ?? 500;
    characteristicsMap = description.toCharacteristicsMapITA();
    addressInfo = description.addressInfo;
  }

  void clearInsideViewControllers() {
    addressInfo = BaseAddressInfo.empty();
    smartAdDescription = SmartAdDescription.empty();
    waitingForGeneration = false;

    roomsController.clear();
    bathroomsController.clear();
    floorController.clear();  
    buildingFloorCountController.clear();
    squareFootageController.clear();
    descriptionController.clear();
    propertyTypeController.clear();
    usageTypeController.clear();
    contractTypeController.clear();
    maintenanceStatusController.clear();
    energyClassController.clear();
    additionalInfoController.clear();
    acquirerTypeController.clear();
    descriptionLanguageController.clear();
    descriptionLength = 500;
    characteristicsMap.clear();
  } 
}