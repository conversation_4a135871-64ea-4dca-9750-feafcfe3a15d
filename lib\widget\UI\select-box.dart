import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class NarSelectBoxWidget extends StatefulWidget {
  final List<String>? options;
  final Function? onChanged;
  final Function? onFieldTap;
  final String? hint;
  final String? disabledHint;
  final bool? autoFocus;
  final TextEditingController? controller;
  final String? validationType;
  final String? parametersValidate;
  final String? label;
  final Color? labelColor;
  final Color? dropdownBorderColor;
  final int? flex;
  final double? borderRadius;
  final EdgeInsets? contentPadding;
  final double dropdownFontSize; 
  final double selectedValueFontSize;
  final bool? enabled;

  NarSelectBoxWidget(
      {Key? key,
      this.flex = 1,
      this.label,
      this.labelColor = const Color(0xff696969),
      this.dropdownBorderColor,
      this.options,
      this.hint,
      this.disabledHint,
      this.controller,
      this.autoFocus = false,
      this.onChanged,
      this.onFieldTap,
      this.validationType,
      this.contentPadding =
          const EdgeInsets.only(top: 17, bottom: 17, right: 2, left: 10),
      this.parametersValidate,
      this.borderRadius = 8,
      this.dropdownFontSize = 14,
      this.selectedValueFontSize = 15,
      this.enabled = true

      })
      : super(key: key);

  @override
  _NarSelectBoxWidgetState createState() => _NarSelectBoxWidgetState();
}

class _NarSelectBoxWidgetState extends State<NarSelectBoxWidget> {
  double bottomPaddingToError = 12;
  String? dropdownValue;
  final GlobalKey<FormFieldState> _key_dropdown = GlobalKey<FormFieldState>(); //<- add this

  @override
  void initState() {
    super.initState();
    
    // hasEmpty();
    
  }

  hasEmpty(){
    bool flag = false;
    for (var i = 0; i < widget.options!.length; i++) {
      if( widget.options![i] == "" ) {
        print('has mepty');
        flag = true;
      } 
    }

    if( flag == false ) { widget.options!.insert(0, ''); }
  }

  @protected
  void didUpdateWidget(NarSelectBoxWidget oldWidget) {
    // hasEmpty();
    super.didUpdateWidget(oldWidget);
    
    // print({ " widget.controller!.text ", widget.controller!.text, widget.options, widget.options!.indexOf(widget.controller!.text) });
    // if( widget.controller!.text == "" ) {
    //   _key_dropdown.currentState?.reset();
    // }

  }

  @override
  Widget build(BuildContext context) {
    
    return AbsorbPointer(
      absorbing: widget.enabled == false ? true : false,
      child: Opacity(
        opacity: widget.enabled == false ? 0.6 : 1.0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            widget.label != null
                ? NarFormLabelWidget(
                    label: widget.label,
                    textColor: Color(0xff696969),
                    fontSize: 13,
                    fontWeight: '600',
                  )
                : Container(),
            widget.label != null ? SizedBox(height: 4) : Container(),
            DropdownButtonFormField<String>(
              key: _key_dropdown,
              isExpanded: true,
              initialValue: widget.controller!.text != '' ? widget.controller!.text : null,
              icon: Padding(
                padding: EdgeInsets.only(right: 15),
                child: SvgPicture.asset(
                  'assets/icons/arrow_down.svg',
                  width: 10,
                  color: Color(0xff7e7e7e),
                ),
              ),
              elevation: 2,
              style: TextStyle(
                overflow: TextOverflow.ellipsis,
                color: Colors.black,
                fontSize: 12.0,
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.normal,
                //fontFamily: 'Visby800'
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius!),
              decoration: InputDecoration(
                filled: true,
        
                fillColor: Colors.white, // Set your desired background color here
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(widget.borderRadius!),
                  ),
                ),
                // prefixIcon: widget.prefixIcon,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(widget.borderRadius!),
                  ),
                  borderSide: BorderSide(
                    color: widget.dropdownBorderColor ?? Color.fromRGBO(227, 227, 227, 1),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(widget.borderRadius!),
                  ),
                  borderSide: BorderSide(
                    color: widget.dropdownBorderColor ?? Color.fromRGBO(227, 227, 227, 1),
                    width: 1,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: widget.dropdownBorderColor ?? Color.fromARGB(255, 234, 28, 28)),
                  borderRadius: BorderRadius.all(
                    Radius.circular(widget.borderRadius!),
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: widget.dropdownBorderColor ?? Color.fromARGB(255, 234, 28, 28)),
                  borderRadius: BorderRadius.all(
                    Radius.circular(widget.borderRadius!),
                  ),
                ),
                hintStyle: TextStyle(
                  color: Colors.grey,
                  fontSize: 15.0,
                  fontWeight: FontWeight.w800,
                  fontStyle: FontStyle.normal,
                  letterSpacing: 0,
                ),
                contentPadding: widget.contentPadding,
                isDense: true,
              ),
              onChanged: (String? value) {
                widget.controller!.text = value!;
        
                if (widget.onChanged != null) {
                  widget.onChanged!(value);
                }
              },
              validator: (value) {
                if (widget.validationType != null &&
                    widget.parametersValidate != null) {
                  if (widget.validationType == 'required' &&
                      (value == null || value == '')) {
                    return widget.parametersValidate;
                  }
                }
        
                return null;
              },
              selectedItemBuilder: (BuildContext context) {
                return widget.options!.map<Widget>((String value) {
                  return NarFormLabelWidget(
                    label: value,
                    textColor: Colors.black,
                    fontSize: widget.selectedValueFontSize,
                    fontWeight: '300',
                    overflow: TextOverflow.ellipsis,
                  );
                }).toList();
              },
              // padding: widget.contentPadding!,
              items: widget.options!.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: NarFormLabelWidget(
                    label: value,
                    textColor: Colors.black,
                    fontSize: widget.dropdownFontSize,
                    fontWeight: '300',
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              dropdownColor: Colors.white,
            ),
          ],
        ),
      ),
    );
  }
}
