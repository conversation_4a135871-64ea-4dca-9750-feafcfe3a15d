import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../routes/agency_routes.dart';

import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/smartAdDescription.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';

import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';

import 'package:newarc_platform/pages/agency/ai_instruments/ad_description_controller.dart';
import 'package:newarc_platform/pages/agency/ai_instruments/ad_description_data_source.dart';

class AdDescriptionView extends StatefulWidget {

  final AgencyUser agencyUser;

  AdDescriptionView({
    super.key,
    required this.agencyUser,
  });

  @override
  State<AdDescriptionView> createState() => _AdDescriptionViewState();
}

class _AdDescriptionViewState extends State<AdDescriptionView> {
  late String viewMode;
  final controller = Get.put<AdDescriptionController>(AdDescriptionController());

  @override
  void initState() {
    viewMode = widget.key == Key('describe-ad') ? 'describe' : 'rewrite';
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timestamp) async {
      await initialFetchDescriptions();
    });
  }

  @override
  void didUpdateWidget(AdDescriptionView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchDescriptions() async {
    setState(() {
      controller.descriptions = [];
      controller.loadingDescriptions = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_SMART_AD_DESCRIPTIONS);
      // filter on agency
      collectionSnapshotQuery = collectionSnapshotQuery.where('agencyId', isEqualTo: widget.agencyUser.agencyId);
      // filter on filters
      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }
      collectionSnapshotQuery = collectionSnapshotQuery.where('isArchived', isEqualTo: false);
      // order
      collectionSnapshotQuery = collectionSnapshotQuery.orderBy('insertTimestamp', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();
      // get address info for each description
      for (var element in collectionSnapshot.docs) {
        try {
          var data = element.data();
          var _tmpDesc = SmartAdDescription.fromDocument(data, element.id);
          // get address info
          DocumentSnapshot<Map<String, dynamic>> addressSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS).doc(_tmpDesc.addressId).get();
          var _tmpAdr = RenovationContactAddress.fromDocument(addressSnapshot.data()!, addressSnapshot.id);
          _tmpDesc.addressInfo = _tmpAdr.addressInfo;
          controller.descriptions.add(_tmpDesc);
        } catch (e) {
          print("Error processing address id for description document: $e");
        }
      }
      setState(() {
        controller.loadingDescriptions = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loadingDescriptions = false;
      });
      debugPrint("Error While fetching smart descriptions: ${e.toString()}");
    }
  }

  NarFormLabelWidget _headerTitle() {
    return NarFormLabelWidget(
      label: viewMode == 'describe'
        ? "Descrivi annuncio"
        : 'Riscrivi annuncio',
      fontSize: 19,
      fontWeight: '700',
      textColor: Colors.black,
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo...",
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery) async {
        if (searchQuery?.isNotEmpty ?? false) {
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<SmartAdDescription> filtered = controller.descriptions.where((descr) {
              final address = descr.addressInfo?.fullAddress?.toLowerCase() ?? "";
              return address.contains(searchQuery.toLowerCase());
            }).toList();
            setState(() {
              controller.descriptions = filtered;
            });
          }
        } else {
          await initialFetchDescriptions();
        }
      },
      suffixIconOnTap: () async {
        if (controller.searchTextController.text.trim().isNotEmpty) {
          List<SmartAdDescription> filtered = controller.descriptions.where((descr) {
            final address = descr.addressInfo?.fullAddress?.toLowerCase() ?? "";
            return address.contains(controller.searchTextController.text.toLowerCase());
          }).toList();
          setState(() {
            controller.descriptions = filtered;
          });
        } else {
          await initialFetchDescriptions();
        }
      },
      selectedFilters: [
        // controller.manufacturerSelectedFilter, controller.collectionSelectedFilter
        ],
      textEditingControllers: [
        // controller.manufacturerFilterController,controller.collectionFilterController
        ],
      filterFields: [
        // {
        //   'Produttore': NarImageSelectBoxWidget(
        //     options: controller.manufacturerDropdownFilterList,
        //     controller: controller.manufacturerFilterController,
        //     onChanged: (value) {
        //       controller.filters.removeWhere((element) {
        //         return element['field'] == 'newarcManufacturerID';
        //       });
        //       controller.filters.add({'field': 'newarcManufacturerID', 'value': value["value"], 'search': 'equal'});
        //       controller.manufacturerSelectedFilter = value["label"];
        //     },
        //   ),
        // },
        // {
        //   'Collezione': NarImageSelectBoxWidget(
        //     options: controller.collectionDropdownFilterList,
        //     controller: controller.collectionFilterController,
        //     onChanged: (value) {
        //       controller.filters.removeWhere((element) {
        //         return element['field'] == 'newarcProductCollectionID';
        //       });
        //       controller.filters.add({'field': 'newarcProductCollectionID', 'value': value["value"], 'search': 'equal'});
        //       controller.collectionSelectedFilter = value["label"];
        //     },
        //   ),
        // },
      ],
      onSubmit: () async {
        await initialFetchDescriptions();
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetchDescriptions();
      },
    );
  }

  void showArchiveConfirmDialog({required BuildContext context,required String id}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog) {
            return Center(
              child: BaseNewarcPopup(
                buttonColor: Theme.of(context).primaryColor,
                title: "Conferma archiviazione",
                buttonText: "Archivia",
                onPressed: () async {
                  try{
                    await FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_SMART_AD_DESCRIPTIONS).doc(id).update({
                        "isArchived": true,
                      });
                    initialFetchDescriptions();
                  } catch (e) {
                    debugPrint("---------- ERROR While Archive smart description ------> ${e.toString()}");
                  }
                },
                column: Container(
                  width: 400,
                  padding: EdgeInsets.symmetric(vertical: 25),
                  child: Center(
                    child: NarFormLabelWidget(
                      label:  "Vuoi veramente archiviare la descrizione?" ,
                      textColor: Color(0xff696969),
                      fontSize: 18,
                      fontWeight: '600',
                    ),
                  ),
                )
              ),
            );
          });
        });
  }

  @override
  Widget build(BuildContext context) {

    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    
    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _headerTitle(),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          SmartAdDescription selectedDescription = SmartAdDescription.empty();
                          controller.setInsideViewControllers(selectedDescription);
                          context.go(AgencyRoutes.agencySmartDescriptionId("nuova"));
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Nuova descrizione",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _filter(),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingDescriptions ? 0.5 : 1,
                        child: NewarcDataTable(
                          dividerThickness: 1,
                          empty: Text(""),
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Indirizzo',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Tipo proprietà',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Stato generazione',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Azioni',
                              ),
                              size: ColumnSize.S
                            ),
                          ],
                          rowsPerPage: 20,
                          source: AdDescriptionDataSource(
                            initialFetchDescriptions: initialFetchDescriptions,
                            onOpenAzioniDialog: (String type, SmartAdDescription adDescription) async {
                              if (type == "edit") {
                                controller.setInsideViewControllers(adDescription);
                                context.go(AgencyRoutes.agencySmartDescriptionId(adDescription.id ?? ""));
                              } else if (type == "archivia") {
                                showArchiveConfirmDialog(context: context, id: adDescription.id ?? "");
                              }
                            },
                            context: context,
                            descriptions: controller.descriptions,
                          ),
                        ),
                      ),
                      if (controller.loadingDescriptions)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
