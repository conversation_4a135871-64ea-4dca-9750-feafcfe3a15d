import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/various.dart';

class NewarcUser {
  String? id;
  String? firstName;
  String? lastName;
  String? email;
  String? type;
  String? role;
  String? profilePicture;
  bool? isActive;
  bool? isArchived;
  bool? isHiredInternally;
  Map? menuAccess;
  Map? projectTabAccess;
  bool? isFilterPerAccountEnabled;
  String? phoneCode;
  String? phone;
  bool? isPasswordSet;
  String? source;
  int? insertTimestamp;

  NewarcUser(Map<String, dynamic> userMap) {
    this.id = userMap['id'];
    this.firstName = userMap['name']??'';
    this.lastName = userMap['surname']??'';
    this.email = userMap['email'];
    this.type = userMap['type'];
    this.role = userMap['role'].toString().toTitleCase().replaceAll(' ', '_');
    this.profilePicture = userMap['profilePicture'];
    this.isActive = userMap['isActive'] != null ? userMap['isActive'] : true;
    this.isArchived = userMap['isArchived'] != null ? userMap['isArchived'] : false;
    this.isHiredInternally = userMap['isHiredInternally'];
    this.menuAccess = userMap['menuAccess'];
    this.projectTabAccess = userMap['projectTabAccess'];
    this.isFilterPerAccountEnabled = userMap['isFilterPerAccountEnabled']??true;
    this.phoneCode = userMap['phoneCode']??'';
    this.phone = userMap['phone']??'';
    this.insertTimestamp = userMap['insertTimestamp']??0;
    this.source = userMap['source']??'direct';
    
  }

  NewarcUser.empty() {
    this.id = '';
    this.firstName = '';
    this.lastName = '';
    this.email = '';
    this.type = '';
    this.role = '';
    this.isActive = true;
    this.profilePicture = '';
    this.isArchived = false;
    this.isHiredInternally = null;
    this.menuAccess = null;
    this.isFilterPerAccountEnabled = true;
    this.projectTabAccess = null;
    this.phone = '';
    this.phoneCode = '';
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.source = 'direct';
  }

  NewarcUser.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.firstName = data['name']??'';
    this.lastName = data['surname']??'';
    this.email = data['email'];
    this.type = data['type']??'direct';
    this.role = data['role']??'';
    this.profilePicture = data['profilePicture'];
    this.isActive = data['isActive'] != null ? data['isActive'] : true;
    this.isArchived = data['isArchived'] != null ? data['isArchived'] : false;
    this.isHiredInternally = data['isHiredInternally'];
    this.menuAccess = data['menuAccess']??null;
    this.isFilterPerAccountEnabled = data['isFilterPerAccountEnabled']??true;
    this.projectTabAccess = data['projectTabAccess']??getDefaultProjectTabByRole(data['role']);
    this.phone = data['phone']??'';
    this.phoneCode = data['phoneCode']??'';
    this.insertTimestamp = data['insertTimestamp']??Timestamp.now().millisecondsSinceEpoch;
    this.source = data['source']??'direct';
  }

  toFullName() {
    return this.firstName! +' '+ this.lastName!;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'name': this.firstName,
      'surname': this.lastName,
      'email': this.email,
      'type': this.type??'direct'.toLowerCase().replaceAll(' ', '_'),
      'role': this.role!.toLowerCase().replaceAll(' ', '_'),
      'profilePicture': this.profilePicture,
      'isActive': this.isActive,
      'isArchived': this.isArchived,
      'isHiredInternally': this.isHiredInternally,
      'menuAccess': this.menuAccess,
      'isFilterPerAccountEnabled': this.isFilterPerAccountEnabled??true,
      'projectTabAccess': this.projectTabAccess??null,
      'phone': this.phone??'',
      'phoneCode': this.phoneCode??'',
      'insertTimestamp': this.insertTimestamp??Timestamp.now().millisecondsSinceEpoch,
      'source': this.source
    };
  }
}
