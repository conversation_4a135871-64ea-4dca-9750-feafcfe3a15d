import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/supplier/dashboard_view.dart';
import 'package:newarc_platform/pages/supplier/settings.dart';
import 'package:newarc_platform/widget/supplier/custom_drawer.dart';
import 'package:newarc_platform/widget/supplier/custom_appbar_menu.dart';

import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:provider/provider.dart';

import '../../routes/user_provider.dart';





class HomeSupplier extends StatefulWidget {
  const HomeSupplier({Key? key, required this.supplierUser}) : super(key: key);

  static const String route = '/home-supplier';
  final SupplierUser supplierUser;

  @override
  State<HomeSupplier> createState() => _HomeSupplierState();
}

class _HomeSupplierState extends State<HomeSupplier> {
  String selectedView = 'dashboard';
  String? profilePicture;
  Map? _projectArguments;

  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    // TODO: implement initState
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await printUrl(
        "${appConfig.COLLECT_USERS}/", widget.supplierUser.id, widget.supplierUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
    // widget.agencyUser.profilePicture!
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context, listen: true);
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        if (constraints.maxWidth > 650) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Row(
              children: [
                CustomDrawer(
                  updateViewCallback: _updateViewCallback,
                  supplierUser: widget.supplierUser,
                  selectedView: selectedView,
                ),
                Expanded(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 15),
                        child: AppBar(
                          backgroundColor: Colors.transparent,
                          elevation: 0,
                          leading: Container(),
                          actions: <Widget>[
                            // getNotificationTray(),
                            getAppbarMenu(profilePicture),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 10.0),
                        child: Container(
                          height: 1,
                          color: Color(0xffe0e0e0),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20),
                          child: selectView(true),
                          // child: Text('Test'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        } else {
          // Versione ridotta
          return Scaffold(
            backgroundColor: Color(0xffF9F9F9),
            appBar: AppBar(
              backgroundColor: Colors.black,
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.settings,
                    color: Colors.grey,
                  ),
                  onPressed: () {
                    // do something
                  },
                ),
                // getNotificationTray(),
                PopupMenuButton(
                  tooltip: "",
                  icon: SvgPicture.asset(
                    'assets/icons/account.svg',
                    color: Colors.grey,
                    width: 20,
                  ),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      enabled: false,
                      child: Column(children: [Text(widget.supplierUser.supplier!.name!), Text(widget.supplierUser.email!)]),
                      value: 1,
                      onTap: () {},
                    ),
                    PopupMenuItem(
                      child: Text("Logout"),
                      value: 2,
                      onTap: () async {
                        // deleteUserRole();
                        await FirebaseAuth.instance.signOut();
                        // Navigator.of(context).pushReplacementNamed(
                        //   LoginPage.route,
                        // );
                      },
                    ),
                  ],
                )
              ],
            ),
            drawer: CustomDrawer(
              updateViewCallback: _updateViewCallback,
              supplierUser: widget.supplierUser,
              selectedView: selectedView,
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: Column(
                children: [
                  Expanded(child: selectView(false)),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  void _updateViewCallback(String newSelectedView, {Map projectArguments = const {}}) {
    setState(() {
      selectedView = newSelectedView;
      if (projectArguments.length > 0) {
        _projectArguments = projectArguments;
      }
    });
  }

  // Widget getNotificationTray() {
  //   var agencyId = widget.agencyUser.agency!.id;
  //   return StreamBuilder(
  //     stream: FirebaseFirestore.instance
  //         .collection(
  //           '${appConfig.COLLECT_AGENCIES}/',
  //         )
  //         .doc(agencyId)
  //         .snapshots(),
  //     builder: (BuildContext context, AsyncSnapshot<DocumentSnapshot<Map<String, dynamic>>> snapshot) {
  //       //List<NewarcNotification> notifications = [];
  //       bool notificationsRead = true;

  //       if (snapshot.hasData) {
  //         Agency agency = Agency.fromDocument(snapshot.data!.data()!, snapshot.data!.id);
  //         notificationsRead = agency.notificationsRead!;
  //         /*snapshot.data!.docs.forEach((doc) {
  //           notifications
  //               .add(NewarcNotification.fromDocument(doc.data(), doc.id));
  //         });*/
  //       }
  //       return Stack(
  //         alignment: Alignment.center,
  //         children: [
  //           Container(
  //             height: 40,
  //             width: 40,
  //             color: Colors.transparent,
  //             child: CustomNotificationTray(agency: widget.agencyUser.agency!, notificationsRead: notificationsRead),
  //           ),
  //           notificationsRead
  //               ? Container()
  //               : Positioned(
  //                   right: 10,
  //                   top: 10,
  //                   child: Container(
  //                     width: 9,
  //                     height: 9,
  //                     decoration: BoxDecoration(
  //                       color: Theme.of(context).primaryColor,
  //                       borderRadius: BorderRadius.circular(20),
  //                     ),
  //                   ),
  //                 )
  //         ],
  //       );
  //     },
  //   );
  // }

  Widget getAppbarMenu(String? profilePicture) {
    return Container(
      height: 40,
      width: 40,
      color: Colors.transparent,
      child: CustomAppbarMenu(
        profilePicture: profilePicture,
        onSettingsTapped: () {
          selectedView = 'supplier-setting';
          _updateViewCallback(selectedView);
        },
      ),
    );
  }

  Widget selectView(bool responsive) {
    if (selectedView == 'dashboard') {
      return Dashboard(supplier: widget.supplierUser.supplier!, supplierUser: widget.supplierUser, responsive: responsive);
    } else if (selectedView == 'supplier-setting') {
      return SupplierSetting(
          supplierUser: widget.supplierUser, getProfilePicture: getProfilePicture);
    } else {
      return Text('La vista selezionata non è disponibile');
    }
  }
}
