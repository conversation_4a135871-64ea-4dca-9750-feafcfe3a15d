import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/smartAdDescription.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/routes/agency_routes.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/pages/agency/ai_instruments/ad_description_controller.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/custom_progress_indicator.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/functions/various.dart';
import '../../../widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConsts;


class InsideAdDescriptionView extends StatefulWidget {
  final AgencyUser agencyUser;
  final String? smartAdDescriptionId;
  const InsideAdDescriptionView({super.key, required this.agencyUser, required this.smartAdDescriptionId});

  @override
  State<InsideAdDescriptionView> createState() => _InsideAdDescriptionViewState();
}

class _InsideAdDescriptionViewState extends State<InsideAdDescriptionView> {

  final controller = Get.put<AdDescriptionController>(AdDescriptionController());
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              IconButton(
                hoverColor: Colors.transparent,
                focusColor: Colors.transparent,
                onPressed: () {
                  context.go(AgencyRoutes.agencySmartDescription);
                },
                icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                    height: 20, color: Colors.black),
              ),
              SizedBox(
                width: 10,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  NarFormLabelWidget(
                    label: "Descrizione",
                    fontSize: 10,
                    fontWeight: '600',
                    textColor: AppColor.greyColor,
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  NarFormLabelWidget(
                    label: controller.addressInfo?.toShortAddress() ?? "Aggiungi nuova descrizione",
                    fontSize: 22,
                    fontWeight: '700',
                    textColor: Colors.black,
                  ),
                ],
              )
            ]),
        SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Container(
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(13),
                    border: Border.all(width: 1, color: Color(0Xffe7e7e7)),
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height * 0.75,
                child: AbsorbPointer(
                  absorbing: controller.waitingForGeneration,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        height: MediaQuery.of(context).size.height * 0.60,
                        child: Form(
                          key: formKey,
                          child: ListView(
                            children: [
                              NarFormLabelWidget(
                                label: "Dati Generazione",
                                textColor: Colors.black,
                                fontSize: 18,
                                fontWeight: '700',
                              ),
                              SizedBox(height: 20),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      key: Key("generation-language"),
                                      label: "Lingua",
                                      controller: controller.descriptionLanguageController,
                                      options: appConsts.languagesList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        formKey.currentState!.validate();
                                        controller.smartAdDescription.generation.generationLanguage = value;
                                      }
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          NarFormLabelWidget(
                                            label: "Caratteri: ",
                                            fontSize: 13,
                                            fontWeight: '600',
                                            textColor: Color(0xff696969),
                                          ),
                                          NarFormLabelWidget(
                                            label: controller.descriptionLength.toInt().toString(),
                                            fontSize: 13,
                                            fontWeight: '800',
                                            textColor: Color(0xff696969),
                                          ),
                                        ],
                                      ),
                                      Slider(
                                        value: controller.descriptionLength, 
                                        min: 100,
                                        max: 2000,
                                        activeColor: Colors.black,
                                        overlayColor: WidgetStatePropertyAll(Colors.black38),
                                        thumbColor: Colors.black,
                                        label: "${controller.descriptionLength.toInt()} caratteri",
                                        onChanged: (value) {
                                          setState(() {
                                            controller.descriptionLength = value;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ]
                              ),
                              SizedBox(height: 30),
                              NarFormLabelWidget(
                                label: "Dati Immobile",
                                textColor: Colors.black,
                                fontSize: 18,
                                fontWeight: '700',
                              ),
                              SizedBox(height: 20),
                              Row(
                                children: [
                                  Expanded(
                                    child: AbsorbPointer(
                                      absorbing: controller.smartAdDescription.addressId != null,
                                      child: AddressSearchBar(
                                        label: "Indirizzo", 
                                        initialAddress: controller.addressInfo?.fullAddress ?? "",
                                        validator: (rawText) {
                                          if (rawText == null || rawText.isEmpty) {
                                            return 'Obbligatorio';
                                          }
                                          return null;
                                        },
                                        onPlaceSelected: (value) {
                                          debugPrint('Selected place: \n$value');
                                          BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(value['place']);
                                          if (selectedAddress.isValidAddress()){
                                            controller.addressInfo = selectedAddress;
                                          } else {
                                            controller.addressInfo = BaseAddressInfo.empty();
                                          }
                                        }
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Categoria",
                                      controller: controller.usageTypeController,
                                      options: appConsts.usageTypesList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        formKey.currentState!.validate();
                                        controller.acquirerTypeController.text = '';
                                        controller.smartAdDescription.usageType = value;
                                        setState(() {});
                                      }
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Acquirente tipo",
                                      enabled: controller.usageTypeController.text != '',
                                      controller: controller.acquirerTypeController,
                                      options: appConsts.acquirerTypesList.where((type) {
                                        bool isResidential = controller.usageTypeController.text != 'Commerciale';
                                        if (isResidential) {
                                          return type != 'Commerciante';
                                        } else {
                                          return type == 'Commerciante';
                                        }
                                      }).toList(),
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        controller.smartAdDescription.acquirerType = value;
                                        formKey.currentState!.validate();
                                      }
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Tipologia",
                                      controller: controller.propertyTypeController,
                                      options: appConsts.propertyTypesList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        controller.smartAdDescription.propertyType = value;
                                        formKey.currentState!.validate();
                                      }
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: "Metri quadri commerciali",
                                    controller: controller.squareFootageController,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Obbligatorio';
                                      }
                                      return null;
                                    },
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))
                                    ],
                                    suffixIcon: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: "mq",
                                          fontSize: 14,
                                          fontWeight: '600',
                                          textColor: Color(0xFF959595),
                                        ),
                                      ],
                                    ),
                                    onChangedCallback: (value) {
                                      controller.smartAdDescription.grossSquareFootage = int.tryParse(value);
                                      formKey.currentState!.validate();
                                    }
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Locali",
                                      controller: controller.roomsController,
                                      options: appConsts.localiList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        controller.smartAdDescription.rooms = int.tryParse(value.replaceAll(r'+', ''));
                                        formKey.currentState!.validate();
                                      }
                                    )
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Bagni",
                                      controller: controller.bathroomsController,
                                      options: appConsts.bagniList,
                                      onChanged: (value) {
                                        controller.smartAdDescription.numberOfBathrooms = int.tryParse(value.replaceAll(r'+', ''));
                                        formKey.currentState!.validate();
                                      }
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Classe energetica",
                                      controller: controller.energyClassController,
                                      options: appConsts.energyClassList,
                                      onChanged: (value) {
                                        controller.smartAdDescription.energyClass = value;
                                        formKey.currentState!.validate();
                                      }
                                    )
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Piano",
                                      controller: controller.floorController,
                                      options: appConsts.pianoList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        controller.smartAdDescription.unitFloor = value;
                                        formKey.currentState!.validate();
                                      }
                                    )
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Piani edificio",
                                      controller: controller.buildingFloorCountController,
                                      options: appConsts.pianoList,
                                      onChanged: (value) {
                                        controller.smartAdDescription.buildingFloorCount = value;
                                        formKey.currentState!.validate();
                                      }
                                    )
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Stato di manutenzione",
                                      controller: controller.maintenanceStatusController,
                                      options: appConsts.mainStatList,
                                      onChanged: (value) {
                                        controller.smartAdDescription.maintenanceStatus = value;
                                        formKey.currentState!.validate();
                                      }
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  Expanded(
                                    child: NarSelectBoxWidget(
                                      label: "Obbiettivo",
                                      controller: controller.contractTypeController,
                                      options: appConsts.contractTypesList,
                                      validationType: 'required',
                                      parametersValidate: 'Obbligatorio',
                                      onChanged: (value) {
                                        controller.smartAdDescription.contractType = value;
                                        formKey.currentState!.validate();
                                      }
                                    )
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: "Informazioni aggiuntive",
                                    controller: controller.additionalInfoController,
                                    minLines: 3,
                                    onChangedCallback: (value){
                                      controller.smartAdDescription.additionalInfo = value;
                                      formKey.currentState!.validate();
                                    },
                                  ),
                                ],
                              ),
                              SizedBox(height: 10),
                              NarFormLabelWidget(label: "Caratteristiche aggiuntive", fontSize: 13, fontWeight: '600', textColor: Color(0xff696969),),
                              SizedBox(height: 5,),
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.only(bottom: 5),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Color(0xffDBDBDB),
                                    width: 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: NarCheckboxWidget(
                                  label: 'Caratteristiche aggiuntive',
                                  values: controller.characteristicsMap,
                                  columns: 4,
                                  fontSize: 13,
                                  childAspectRatio: 5,
                                  selectionBackgroundColor: Color(0xff499B79),
                                  onChanged: (value) {
                                    debugPrint(value.toString());
                                  },
                                ),
                              ),
                              SizedBox(height: 10),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 5),
                      BaseNewarcButton(
                        buttonText: controller.waitingForGeneration ? "" : "Genera Descrizione",
                        height: 50,
                        color: Color(0xff499B79),
                        fontSize: 18,
                        suffixIcon: controller.waitingForGeneration 
                          ? CustomProgressIndicator(assetPath: 'assets/icons/smart-ai.png',  color: Colors.white, animationType: 'disappearing',)
                          : Row(
                            children: [
                              SizedBox(width: 10,),
                              Icon(Icons.auto_awesome_rounded, color: Colors.white, size: 30,),
                            ],
                          ),
                        onPressed: controller.waitingForGeneration 
                        ? (){} : () async {
                          debugPrint('Generate Description');
                          if (formKey.currentState!.validate()){
                            setState(() {
                              controller.waitingForGeneration = true;
                            });
                            try {
                              // save address if new
                              if (controller.smartAdDescription.addressId == null) {
                                RenovationContactAddress renovationContactAddress = RenovationContactAddress.empty();
                                renovationContactAddress.addressInfo = controller.addressInfo;
                                
                                String newAddressId = await writeDocument(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS, renovationContactAddress.toMap());
                                controller.smartAdDescription.addressId = newAddressId;
                                controller.smartAdDescription.addressInfo = controller.addressInfo;
                              }
                              // call ad description generation endpoint
                              Map response = await callSmartDescriptionEndpoint(
                                addressId: controller.smartAdDescription.addressId ?? "", 
                                outputLanguage: controller.descriptionLanguageController.text,
                                outputLength: controller.descriptionLength.toString(),
                                grossSquareFootage: controller.squareFootageController.text,
                                propertyType: controller.propertyTypeController.text,
                                maintenanceStatus: controller.maintenanceStatusController.text != "" ? controller.maintenanceStatusController.text : null,
                                rooms: controller.roomsController.text != "" ? controller.roomsController.text : null,
                                numberOfBathrooms: controller.bathroomsController.text != "" ? controller.bathroomsController.text : null,
                                unitFloor: controller.floorController.text != "" ? controller.floorController.text : null,
                                buildingFloorCount: controller.buildingFloorCountController.text != "" ? controller.buildingFloorCountController.text : null,
                                energyClass: controller.energyClassController.text != "" ? controller.energyClassController.text : null,
                                usageType: controller.usageTypeController.text != "" ? controller.usageTypeController.text : null,
                                contractType: controller.contractTypeController.text != "" ? controller.contractTypeController.text : null,
                                probableBuyerType: controller.acquirerTypeController.text != "" ? controller.acquirerTypeController.text : null,
                                additionalInfo: controller.additionalInfoController.text != "" ? controller.additionalInfoController.text : null,
                                characteristics: controller.characteristicsMap,
                              );
                              // save smart ad description
                              controller.smartAdDescription.agencyId = widget.agencyUser.agencyId;
                              controller.smartAdDescription.userId = widget.agencyUser.id;
                              controller.smartAdDescription.insertTimestamp = DateTime.now().millisecondsSinceEpoch;
                              controller.smartAdDescription.generation.generationCharactersLenght = controller.descriptionLength.toInt().toDouble();
                              controller.smartAdDescription.updateFromCharacteristicsMapITA(controller.characteristicsMap);
                              if (response["status"] == 200) {
                                debugPrint("Description generated");
                                controller.smartAdDescription.generation.generatedDescription = response['msg']['description'];
                                controller.smartAdDescription.generation.generationTokenUsage = response['msg']['token_usage'];
                                controller.smartAdDescription.generation.generationTimeS = response['msg']['generation_time'];
                                controller.smartAdDescription.generation.generationError = null;
                                controller.smartAdDescription.generation.generationStatus = true;
                                controller.smartAdDescription.generation.insertTimestamp = DateTime.now().millisecondsSinceEpoch;
                              } else {
                                debugPrint("Error from heroku ai server: ${response['error']}");
                                controller.smartAdDescription.generation.generatedDescription = null;
                                controller.smartAdDescription.generation.generationTokenUsage = null;
                                controller.smartAdDescription.generation.generationTimeS = null;
                                controller.smartAdDescription.generation.generationError = response['error'];
                                controller.smartAdDescription.generation.generationStatus = false;
                                controller.smartAdDescription.generation.insertTimestamp = DateTime.now().millisecondsSinceEpoch;
                              }
                              if (controller.smartAdDescription.id == null) {
                                controller.smartAdDescription.id = await writeDocument(appConfig.COLLECT_SMART_AD_DESCRIPTIONS, controller.smartAdDescription.toMap());
                                controller.descriptions.add(controller.smartAdDescription);
                              } else {
                                await updateDocument(appConfig.COLLECT_SMART_AD_DESCRIPTIONS, controller.smartAdDescription.id!, controller.smartAdDescription.toMap());
                              }
                            } catch (e) {
                              print("Error generating description: $e");
                            }
                            controller.descriptionController.text = controller.smartAdDescription.generation.generatedDescription ?? "Errore nella generazione della descrizione.";
                            setState(() {
                              controller.waitingForGeneration = false;
                            });
                          };
                        },
                      ),
                    ],
                  ),
                )
              )
            ),
            SizedBox(width: 20),
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                        color: Color(0xffDAEAE3),
                        borderRadius: BorderRadius.circular(13),
                        border: null,
                    ),
                    child: Stack(
                      alignment: Alignment.topRight,
                      clipBehavior: Clip.none,
                      children: [
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            child: Icon(
                              Icons.copy, 
                              color: Color(0xff499B79), 
                              size: 20,
                            ),
                            onTap: (){
                              Clipboard.setData(
                                ClipboardData(
                                  text: controller.descriptionController.text
                                  )
                              );
                              ScaffoldMessenger.of(context)
                                .showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      "Copied to clipboard!"
                                    )
                                  )
                                );
                            },
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label: "Descrizione generata",
                              textColor: Color(0xff499B79),
                              fontSize: 18,
                              fontWeight: '700',
                            ),
                            SizedBox(height: 20),
                            Row(
                              children: [
                                CustomTextFormField(
                                  label: "",
                                  labelColor: Colors.white,
                                  controller: controller.descriptionController,
                                  readOnly: false,
                                  isHaveBorder: false,
                                  minLines: MediaQuery.of(context).size.height ~/ 50,
                                ),
                              ],
                            ),
                          ],
                        )
                      ]
                    )
                  ),
                ],
              )
            ),
          ],
        )
      ],
    );
  }
}
