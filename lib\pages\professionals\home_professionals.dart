import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/routes/professional_routes.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/professionals/dashboard_view.dart';
import 'package:newarc_platform/pages/professionals/settings.dart';
import 'package:newarc_platform/widget/professionals/custom_drawer.dart';
import 'package:newarc_platform/widget/professionals/custom_appbar_menu.dart';
import 'package:newarc_platform/pages/professionals/immagina/immagina_professionals_view.dart';
import 'package:newarc_platform/pages/agency/inside_request_view.dart';
import 'package:newarc_platform/pages/agency/inside_project_view.dart';

import 'package:newarc_platform/app_config.dart' as appConfig;





class HomeProfessionals extends StatefulWidget {
  const HomeProfessionals({Key? key, required this.professionalsUser,required this.child}) : super(key: key);

  final ProfessionalsUser professionalsUser;
  final Widget child;

  @override
  State<HomeProfessionals> createState() => _HomeProfessionalsState();
}

class _HomeProfessionalsState extends State<HomeProfessionals> {
  String? profilePicture;
  Map? _projectArguments;

  var appBarHeight = AppBar().preferredSize.height;

  @override
  void initState() {
    getProfilePicture();
    super.initState();
  }

  getProfilePicture() async {
    var url = await printUrl(
        "${appConfig.COLLECT_PROFESSIONALS}/", widget.professionalsUser.id, widget.professionalsUser.profilePicture);

    if (url != '') {
      setState(() {
        profilePicture = url;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          if (constraints.maxWidth > 650) {
            return Scaffold(
              backgroundColor: Colors.transparent,
              body: Row(
                children: [
                  CustomDrawer(
                    professionalsUser: widget.professionalsUser,
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(right: 15),
                          color: Colors.transparent,
                          child: AppBar(
                            backgroundColor: Colors.transparent,
                            elevation: 0,
                            leading: Container(),
                            actions: <Widget>[
                              Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  NarFormLabelWidget(
                                      label: widget.professionalsUser.name! +
                                          ' ' +
                                          widget.professionalsUser.surname!),
                                  NarFormLabelWidget(
                                    label: "Professional",
                                    textColor: Color(0xff696969),
                                    fontWeight: '500',
                                    fontSize: 11,
                                    letterSpacing: 0.4,
                                  )
                                ]),
                              SizedBox(width: 10),
                              getAppbarMenu(profilePicture),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Container(
                            height: 1,
                            color: Color(0xffe0e0e0),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20),
                            child: widget.child,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Versione ridotta
            return Scaffold(
              backgroundColor: Color(0xffF9F9F9),
              appBar: AppBar(
                backgroundColor: Colors.black,
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: Colors.grey,
                    ),
                    onPressed: () {
                      // do something
                    },
                  ),
                  PopupMenuButton(
                    tooltip: "",
                    icon: SvgPicture.asset(
                      'assets/icons/account.svg',
                      color: Colors.grey,
                      width: 20,
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        child: Column(children: [Text(widget.professionalsUser.professional!.companyName!), Text(widget.professionalsUser.email!)]),
                        value: 1,
                        onTap: () {},
                      ),
                      PopupMenuItem(
                        child: Text("Logout"),
                        value: 2,
                        onTap: () async {
                          await FirebaseAuth.instance.signOut();
                          context.go("/login/professionals");
                        },
                      ),
                    ],
                  )
                ],
              ),
              drawer: CustomDrawer(
                professionalsUser: widget.professionalsUser,
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                child: Column(
                  children: [
                    Expanded(child: widget.child),
                  ],
                ),
              ),
            );
          }
        },
      ),
    );
  }


  Widget getAppbarMenu(String? profilePicture) {
    return Container(
      height: 40,
      width: 40,
      margin: EdgeInsets.only(right: 25),
      color: Colors.white,
      child: CustomAppbarMenu(
        profilePicture: profilePicture,
        onSettingsTapped: () {
          context.go(ProfessionalRoutes.professionalSetting);
        },
      ),
    );
  }

}
