import 'dart:developer';

import 'package:flutter/material.dart';
import '../classes/agencyUser.dart';
import '../classes/professionals.dart';
import '../classes/supplier.dart';
import '../classes/user.dart';


class UserProvider extends ChangeNotifier {
  NewarcUser? _workUser;
  AgencyUser? _agencyUser;
  SupplierUser? _supplierUser;
  ProfessionalsUser? _professionalsUser;
  bool isInitialized = false;
  String accessType = '';
  bool _isLoading = true;
  bool get isLoading => _isLoading;

  void setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  NewarcUser? get workUser => _workUser;
  AgencyUser? get agencyUser => _agencyUser;
  SupplierUser? get supplierUser => _supplierUser;
  ProfessionalsUser? get professionalsUser => _professionalsUser;

  bool get isLoggedIn =>
      _workUser != null || _agencyUser != null || _supplierUser != null || _professionalsUser != null;

  void setWorkUser(NewarcUser user) {
    _workUser = user;
    notifyListeners();
  }

  void setAgencyUser(AgencyUser user) {
    _agencyUser = user;
    notifyListeners();
  }

  void setSupplierUser(SupplierUser user) {
    _supplierUser = user;
    notifyListeners();
  }

  void markInitialized() {
    isInitialized = true;
    notifyListeners();
  }

  void setProfessionalsUser(ProfessionalsUser user) {
    _professionalsUser = user;
    notifyListeners();
  }

  void setAccessType(String  _accessType) {
    accessType = _accessType;
    notifyListeners();
  }

  void clear() {
    _workUser = null;
    _agencyUser = null;
    _supplierUser = null;
    _professionalsUser = null;
    isInitialized = true;
    accessType = "";
    notifyListeners();
  }
}
