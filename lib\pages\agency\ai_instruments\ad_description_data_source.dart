import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/smartAdDescription.dart';
import 'package:newarc_platform/pages/work/pavimenti/pavimenti_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import '../../../widget/UI/image_dropdown.dart';
import '../../../widget/UI/tab/status_widget.dart';


class AdDescriptionDataSource extends DataTableSource {
  final BuildContext context;
  final List<SmartAdDescription> descriptions;
  final Function() initialFetchDescriptions;
  final Function(String type, SmartAdDescription desc) onOpenAzioniDialog;
  final controller = Get.put<PavimentiController>(PavimentiController());


  AdDescriptionDataSource({
    required this.context,
    required this.descriptions,
    required this.initialFetchDescriptions,
    required this.onOpenAzioniDialog,
  });

  @override
  DataRow? getRow(int index) {
    if (index < descriptions.length) {
      SmartAdDescription desc = descriptions[index];
      late Color statusColor;
      desc.generation.generationStatus == true
        ? statusColor = Color(0xff39C14F)
        : desc.generation.generationStatus == false 
          ? statusColor  = Color(0xffDD0000)
          : statusColor = Color(0xff808080);
      late String statusText;
      desc.generation.generationStatus == true
        ? statusText = "Generazione completata"
        : desc.generation.generationStatus == false 
          ? statusText = "Generazione fallita"
          : statusText = "In generazione";
      return DataRow2(
        specificRowHeight: 50,
        cells: [
          //?---- Indirizzo
          DataCell(
            NarFormLabelWidget(
              label: desc.addressInfo?.toShortAddress() ?? "", 
              fontSize: 12, 
              fontWeight: '600', 
              textAlign: TextAlign.start, 
              textColor: Colors.black,),
          ),
          //?---- Tipo proprietà
          DataCell(
            NarFormLabelWidget(
              label: desc.propertyType ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.visible,
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          //? ---- Stato generazione
          DataCell(
            StatusWidget(
              statusColor: statusColor,
              status: statusText,
            )
          ),
          //---- Azioni
          DataCell(_buildActionsDropdown(description: desc)),
        ],
      );
    }

    return null;
  }

  Widget _buildActionsDropdown({required SmartAdDescription description}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: [
          {
            'value': 'edit',
            'label': 'Modifica',
            'image': 'assets/icons/edit.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'archivia',
            'label': 'Archivia',
            'image': 'assets/icons/archive.png',
            'iconColor': AppColor.redColor,
            'labelColor': AppColor.redColor
          },
        ],
        hintText: "Azioni",
        onChanged: (value) {
          if (value == null || value['value'] == null) return;
          onOpenAzioniDialog(value['value'].toString().trim(), description);
        },
      ),
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => descriptions.length;

  @override
  int get selectedRowCount => 0;
}
