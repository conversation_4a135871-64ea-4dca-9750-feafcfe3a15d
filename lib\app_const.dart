import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/various.dart' as various;






List<Map> professionalActivities = [
    {'value': '', 'label': '', 'category': ''},
    {
      'value': 'Demolizioni',
      'label': 'Demolizioni',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Costruzioni',
      'label': 'Costruzioni',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Massetto',
      'label': 'Massetto',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Posa pavimenti',
      'label': 'Posa pavimenti',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Rivestimenti',
      'label': 'Rivestimenti',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Smaltimento macerie',
      'label': 'Smaltimento macerie',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Controsoffitti',
      'label': 'Controsoffitti',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Rasature',
      'label': 'Rasature',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Decorazioni e finiture',
      'label': 'Decorazioni e finiture',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Posa battiscopa',
      'label': 'Posa battiscopa',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Pulizia generale',
      'label': 'Pulizia generale',
      'category': 'Demolizioni/Costruzioni',
    },
    {
      'value': 'Scarico impianti',
      'label': 'Scarico impianti',
      'category': 'Idraulico',
    },
    {
      'value': 'Impianto idraulico',
      'label': 'Impianto idraulico',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa sanitari',
      'label': 'Posa sanitari',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa mobile lavabo',
      'label': 'Posa mobile lavabo',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa piatto doccia',
      'label': 'Posa piatto doccia',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa porta doccia',
      'label': 'Posa porta doccia',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa rubinetteria doccia',
      'label': 'Posa rubinetteria doccia',
      'category': 'Idraulico',
    },
    {
      'value': 'Impianto gas',
      'label': 'Impianto gas',
      'category': 'Idraulico',
    },
    {
      'value': 'Predisposizione clima',
      'label': 'Predisposizione clima',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa termosifoni',
      'label': 'Posa termosifoni',
      'category': 'Idraulico',
    },
    {
      'value': 'Posa caldaia',
      'label': 'Posa caldaia',
      'category': 'Idraulico',
    },
    {
      'value': 'Impianto elettrico',
      'label': 'Impianto elettrico',
      'category': 'Elettricista',
    },
    {
      'value': 'Montaggio luci',
      'label': 'Montaggio luci',
      'category': 'Elettricista',
    },
    {
      'value': 'Posa boiler elettrico',
      'label': 'Posa boiler elettrico',
      'category': 'Elettricista',
    },
    {
      'value': 'Posa parquet',
      'label': 'Posa parquet',
      'category': 'Palchettista',
    },
    {
      'value': 'Posa porte',
      'label': 'Posa porte',
      'category': 'Porte e infissi',
    },
    {
      'value': 'Posa porta blindata',
      'label': 'Posa porta blindata',
      'category': 'Porte e infissi',
    },
    {
      'value': 'Posa infissi',
      'label': 'Posa infissi',
      'category': 'Porte e infissi',
    },
    {
      'value': 'Da definire',
      'label': 'Da definire',
      'category': 'Attività newarc',
    },
  ];

// dict for user roles
// keys: firebase user roles
// values: user roles as they are displayed in the app
Map<String, String> userRolesDict = 
  {
    "scouter": "Commerciale",
    "renovator": "Architetto",
    "renderist": "Renderist",
    "master-renderist": "Head of CGI",
    "geometra": "Geometra",
    "media_creator": "Media Creator",
    "administration": "Amministrazione",
    "finance": "Finance",
    "master": "Master",
  };

List<String> roomsList = [
  'Ingresso',
  'Zona Living',
  'Salotto',
  'Sala da pranzo',
  'Open space',
  'Cucina',
  'Cucina a vista',
  'Cucina separata',
  'Cucinino',
  'Corridoio',
  'Camera da letto',
  'Camera da letto padronale',
  'Cabina armadio',
  'Cameretta',
  'Bagno',
  'Bagno principale',
  'Bagno secondario',
  'Bagno ensuite',
  'Ripostiglio',
  'Lavanderia',
  'Studio',
  'Mansarda',
  'Balcone',
  'Terrazzo',
  'Giardino',
  'Ascensore',
  'Cantina',
  'Soffitta',
  'Androne',
  'Vano scala',
  'Box auto',
  'Esterni',
];

List<String> roomConfiguratorList = [
  "Zona living",
  "Sala da pranzo",
  "Salotto",
  "Cucina",
  "Camera matrimoniale",
  "Seconda camera",
  "Terza camera",
  "Quarta camera",
  "Studio",
  "Primo bagno",
  "Secondo bagno",
  "Terzo bagno",
  "Quarto bagno",
  "Lavanderia",
  "Balcone/Terrazzo",
];


List<String> cities = [
  "Tutte",
  "Torino",
  "Milano",
  "Roma",
];

List<String> zonesList = [
  'Campidoglio',
  'Colle della Maddalena',
  'San Secondo',
  'Pozzo Strada',
  'Crocetta',
  'San Salvario - Baretti',
  'Sassi',
  'Parella',
  'Lingotto',
  'Piazza Solferino',
  'Quadrilatero Romano',
  'Cavoretto',
  'Madonna del Pilone',
  'Superga',
  'Via della Rocca',
  'Santa Rita',
  'Via Roma',
  'Cit Turin',
  'Mirafiori Nord',
  'San Donato',
  'Gran Madre - Crimea',
  'San Salvario - Dante',
  'San Paolo',
  'Cenisia',
  'Nizza Millefonti',
  'Regio Parco',
  'Cittadella',
  'Città Giardino',
  'Vanchiglia',
  'Vanchiglietta',
  'Centro Europa',
  'Giardini Reali',
  'Italia 61',
  'Buenos Aires'
];

Map<String, List<String>> cityZones = {
  'Torino': [
    "santa rita",
    "pozzo strada",
    "crocetta",
    "parella",
    "san salvario - dante",
    "san paolo",
    "borgo vittoria",
    "barriera di milano",
    "mirafiori sud - onorato vigliani",
    "cit turin",
    "cenisia",
    "san donato",
    "mirafiori nord",
    "madonna di campagna",
    "nizza millefonti",
    "aurora",
    "lucento",
    "via roma",
    "lingotto",
    "vanchiglietta",
    "quadrilatero romano",
    "campidoglio",
    "vanchiglia",
    "san secondo",
    "mirafiori sud - strada del drosso",
    "cittadella",
    "via della rocca",
    "regio parco",
    "rebaudengo",
    "san salvario - baretti",
    "centro europa",
    "le vallette",
    "gran madre - crimea",
    "don bosco",
    "barriera di lanzo",
    "cavoretto",
    "piazza solferino",
    "madonna del pilone",
    "barca",
    "giardini reali",
    "falchera",
    "colle della maddalena",
    "superga",
    "città giardino",
    "parco dora",
    "sassi",
    "bertolla",
    "italia 61",
  ],
  'Milano': [
    "città studi",
    "san siro",
    "sempione",
    "buenos aires",
    "certosa",
    "cenisio",
    "centrale",
    "navigli - darsena",
    "famagosta",
    "porta romana - medaglie d'oro",
    "dergano",
    "corvetto",
    "vigentino - fatima",
    "solari",
    "bovisa",
    "paolo sarpi",
    "monte rosa - lotto",
    "maggiolina",
    "isola",
    "giambellino",
    "ripamonti",
    "affori",
    "pasteur",
    "parco trotter",
    "ghisolfa - mac mahon",
    "indipendenza",
    "martini - insubria",
    "lodi - brenta",
    "argonne - corsica",
    "washington",
    "morgagni",
    "tre castelli - faenza",
    "cimiano",
    "cadore",
    "vercelli - wagner",
    "pezzotti - meda",
    "casoretto",
    "porta venezia",
    "farini",
    "plebisciti - susa",
    "corso genova",
    "palestro",
    "moscova",
    "cermenate - abbiategrasso",
    "piave - tricolore",
    "gallaratese",
    "crescenzago",
    "precotto",
    "turro",
    "cascina dei pomi",
    "ticinese",
    "gambara",
    "bocconi",
    "rovereto",
    "barona",
    "cantalupa - san paolo" "bande nere" "niguarda",
    "baggio",
    "viale ungheria - mecenate",
    "tripoli - soderini",
    "cascina merlata - musocco",
    "carrobbio",
    "quadronno - crocetta",
    "porta vittoria",
    "repubblica",
    "ponte nuovo",
    "piazza napoli",
    "montenero",
    "garibaldi - corso como",
    "dezza",
    "piazzale siena",
    "udine",
    "corso san gottardo",
    "zara",
    "frua",
    "bruzzano",
    "amendola - buonarroti",
    "duomo",
    "porta nuova",
    "vincenzo monti",
    "portello - parco vittoria",
    "guastalla",
    "istria",
    "quartiere adriano",
    "missori",
    "gorla",
    "villa san giovanni",
    "molise - cuoco",
    "ascanio sforza",
    "de angeli",
    "quarto oggiaro",
    "arena",
    "comasina",
    "lambrate",
    "chiesa rossa",
    "pagano",
    "san carlo",
    "bicocca",
    "vialba",
    "greco - segnano",
    "bisceglie",
    "inganni",
    "brera",
    "melchiorre gioia",
    "arco della pace",
    "gratosoglio",
    "santa giulia",
    "turati",
    "prato centenaro",
    "city life",
    "bologna - sulmona",
    "quartiere forlanini",
    "san vittore",
    "ponte lambro",
    "primaticcio",
    "rubattino",
    "cadorna - castello",
    "quintosole - chiaravalle",
    "bignami - ponale",
    "lorenteggio",
    "sant'ambrogio",
    "san babila",
    "ca' granda",
    "ortica",
    "borgogna - largo augusto",
    "lanza",
    "quartiere feltre",
    "trenno",
    "quarto cagnino",
    "roserio",
    "quadrilatero della moda",
    "quinto romano",
    "figino",
    "monte stella",
    "muggiano",
    "quartiere olmi",
    "bovisasca",
    "scala - manzoni",
    "rogoredo",
    "qt8",
    "cascina gobba",
    "parco lambro",
  ],
  'Roma': [
    "torrevecchia",
    "cinecittà",
    "alessandrino - torre spaccata",
    "talenti - monte sacro",
    "centocelle",
    "borghesiana",
    "balduina",
    "casalotti",
    "tor sapienza - la rustica",
    "marconi",
    "pisana - bravetta",
    "città giardino",
    "camilluccia - farnesina",
    "pigneto",
    "africano - villa chigi",
    "acilia",
    "infernetto",
    "ponte di nona",
    "ardeatino - montagnola",
    "ostia ponente",
    "nuovo salario",
    "gregorio vii - piccolomini",
    "furio camillo",
    "torre angela",
    "colli portuensi - casaletto",
    "ostia levante",
    "tomba di nerone",
    "bologna",
    "torrino",
    "cortina d'ampezzo",
    "monteverde nuovo",
    "casal bruciato",
    "castelverde",
    "vigna clara - vigna stelluti",
    "trullo - colle del sole",
    "morena",
    "fleming",
    "mazzini - delle vittorie",
    "garbatella",
    "san giovanni",
    "la storta",
    "parioli",
    "trieste - coppedè",
    "trastevere",
    "tor de' schiavi",
    "casal palocco",
    "villa gordiani",
    "monteverde vecchio",
    "mezzocammino",
    "magliana",
    "euclide",
    "tor tre teste - torre maura",
    "battistini - primavalle",
    "fonte ostiense",
    "rocca cencia",
    "portuense",
    "pietralata",
    "aurelio - val cannuta",
    "la giustiniana",
    "fidene",
    "quadraro",
    "anagnina",
    "dragona",
    "san paolo",
    "flaminio",
    "prati",
    "monte mario - trionfale",
    "appio latino",
    "castel di leva",
    "tor vergata",
    "ottavia",
    "mostacciano",
    "monti tiburtini",
    "ponte mammolo",
    "torpignattara",
    "conca d'oro - valli",
    "cecchignola - giuliano dalmata",
    "boccea",
    "grottarossa - saxa rubra",
    "casetta mattei - corviale",
    "baldo degli ubaldi",
    "eur",
    "cipro",
    "tintoretto",
    "prima porta",
    "torre gaia",
    "esquilino",
    "ostia antica",
    "romanina",
    "re di roma",
    "ponte galeria",
    "finocchio",
    "casal bertone",
    "san lorenzo",
    "san basilio",
    "cornelia - montespaccato",
    "villa fiorelli",
    "labaro",
    "collina delle muse",
    "pinciano - villa ada",
    "spinaceto",
    "tufello",
    "villa bonelli",
    "colli albani",
    "giardinetti",
    "cassia - san godenzo",
    "monti",
    "torresina",
    "axa",
    "valle muricana",
    "colli aniene",
    "olgiata",
    "porta di roma",
    "appio claudio - statuario",
    "policlinico",
    "trigoria",
    "fontana candida",
    "gemelli - pineta sacchetti",
    "salario - porta pia",
    "rinnovamento",
    "settecamini",
    "fonte meravigliosa",
    "settebagni",
    "malafede",
    "piazzale degli eroi",
    "corcolle",
    "spagna",
    "piazza navona",
    "quarto miglio",
    "vitinia",
    "tor de' cenci",
    "ostiense",
    "ponte milvio",
    "massimina",
    "sallustiano",
    "borgo",
    "casal lumbroso",
    "monte migliore",
    "colle oppio",
    "medaglie d'oro",
    "navigatori",
    "cesano",
    "casal bernocchi",
    "bufalotta",
    "vallerano",
    "casal selce",
    "parco de' medici - muratella",
    "casal boccone",
    "arco di travertino",
    "valle santa",
    "repubblica",
    "casal monastero",
    "case rosse",
    "piana del sole",
    "roma 70",
    "piazza del popolo",
    "colosseo - fori imperiali",
    "san saba - caracalla",
    "testaccio",
    "due ponti",
    "valle aurelia",
    "serpentara",
    "campo de' fiori",
    "osteria nuova",
    "montecitorio",
    "ghetto - portico d'ottavia",
    "castro pretorio",
    "vigne nuove - casale nei",
    "via giulia",
    "centro giano",
    "capannelle - statuario",
    "vittorio veneto",
    "pantheon",
    "lunghezza",
    "tor cervara",
    "aventino",
    "barberini",
    "villaggio olimpico",
    "largo argentina",
    "appia pignatelli",
    "castel di guido",
    "torricola - tor carbone",
    "fonte laurentina",
    "trevi",
    "castel fusano",
    "santa fumia",
    "san vittorino",
    "tor pagnotta",
    "maglianella",
    "divino amore",
    "tiberina",
    "falcognana",
    "fioranello",
    "pian savelli",
    "castel romano",
    "tor di quinto",
  ],
};

List<String> bagniList = const ["1", "2", "3+"];
List<String> localiList = const ["1", "2", "3", "4", "5+"];
List<String> pianoList = const ["Piano terra / rialzato", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10+"];
List<String> mainStatList = const ["Nuovo / In Costruzione", "Ottimo / Ristrutturato", "Buono / Abitabile", "Da ristrutturare"];
List<String> contractTypesList = const ["Vendita", "Affitto"];
List<String> languagesList = const ["Italiano", "Inglese", "Francese", "Tedesco"];
List<String> immaginaRequestStatuses = [
  'da completare',
  'in analisi',
  'bloccata',
  'confermata'
];

List<String> installmentList = [
  '20 - 10 - 30 - 30 - 10',
  '10 - 20 - 30 - 30 - 10',
  '10 - 10 - 30 - 30 - 20',
  '10 - 20 - 25 - 25 - 20',
  '10 - 25 - 30 - 30 - 5',
  '5 - 25 - 35 - 30 - 5',
  '5 - 25 - 30 - 30 - 10',
  '5 - 25 - 25 - 25 - 20',
  '5 - 15 - 30 - 30 - 20'
];

Map<String, Map<String, String>> composeProjectCode = {
  'region': {
    'Liguria': "00",
    'Piemonte': '01', 
    'Lombardia': '02', 
    'Lazio': '03',
    'Campania': '04',
    'Sicilia': '05',
    'Veneto': '06',
    'Emilia-Romagna': '07',
    'Toscana': '08',
    'Puglia': '09',
    'Calabria': '10',
    'Sardegna': '11',
    'Marche': '12',
    'Abruzzo': '13',
    'Friuli-Venezia Giulia': '14',
    'Trentino-Alto Adige': '15',
    'Umbria': '16',
    'Basilicata': '17',
    'Molise': '18',
    'Valle d\'Aosta': '19',
  },
  'project': {
    'ristrutturazione': 'RI',
    'subito': 'SU',
    'insieme': 'IN',
    'immagina': 'IM'
  }
};

Map cityToRegionConverter = {
  'Torino': 'Piemonte',
  'Milano': 'Lombardia',
  'Roma': 'Lazio',
};

Map regionToCityConverter = {
  'Piemonte': 'Torino',
  'Lombardia': 'Milano',
  'Lazio': 'Roma',
};

String BASE_NEWARC_IMMAGINA_SUCCESS_FEE = '0.2';

List<String> energyClassList = ['A4', 'A3', 'A2', 'A1', 'B', 'C', 'D', 'E', 'F', 'G'];

Map<String, bool> houseFeatures = {
    'Ascensore': false,
    'Cantina': false,
    'Pred. condizionatore': false,
    'Porta blindata': false,
    'Fibra ottica': false,
    'Impianto TV': false,
    'Infissi ad alta efficienza': false,
    'Pred. antifurto': false,
    'Terrazzo': false,
    'Giardino condominiale': false,
    'Giardino privato': false,
    'Portineria': false,
    'Tapparelle motorizzate': false,
    'Tapparelle domotizzate': false,
    'Luci domotizzate': false,
    'Risc. centralizzato': false,
    'Risc. autonomo': false,
    'Piano alto': false,
    'Vicinanza Metro': false,
    'Ampi balconi': false,
    'Doppia esposizione': false,
    'Tripla esposizione': false,
    'Quadrupla esposizione': false,
    'Grande zona living': false,
    'Doppi servizi': false,
    'Stabile signorile': false,
    'Stabile videosorvegliato': false,
    'Box o garage': false,
    'Piscina': false,
    'Cabina armadio': false,
    'Fotovoltaico': false,
  };

  List<String> propertyTypesList = [
    'Appartamento',
    'Villa',
    'Casa indipendente',
    'Rustico / Casale',
    'Loft / Open Space',
    'Attico',
    'Mansarda',
    'Palazzo / Stabile'
  ];

  List<String> acquirerTypesList = [
    'Single',
    'Coppia',
    'Famiglia',
    'Investitore',
    'Casa vacanze',
    'Commerciante',
  ];

  String newarcAdsBaseUrl = appConfig.isProduction ? "https://compra.newarc.it/compra/" : "https://newarc-staging.web.app/compra/";

  List<String> supplierFormationTypesList = [
    "Srl",
    "Srls",
    "Impresa individuale",
    'SNC',
    'SAS',
    'Cooperativa',
    'Spa'
  ];

  List<String> professionalsTypesList = [
    "Architetto",
    "Studio Professionale",
    "Sviluppatore immobiliare",
    "Costruttore",
    "Geometra",
    "Interior Designer",
    "Ditta Edile",
    "Ingegnere Edile",
    "Altro",
  ];

Map<String, Map<String, dynamic>> projectSectionAccess = {
  "generali": {
    'status': 'show',
    'children': {
      'Dashboard': 'show',
      'Immobile': 'show',
      'Team Newarc': 'show',
    }
  },
  "acquisizione/rivendita": {
    'status': 'show',
    'children': {
      'Agenzia': 'show',
      'Annuncio': 'show'
    }
  },
  "ristrutturazione": {
    'status': 'show',
    'children': {
      'Gestisci Lavori': 'show',
      'Gestisci Materiali': 'show',
      'Aggiornamenti cantiere': 'show',
    }
  },
  "economics": {
    'status': 'show',
    'children': {
      'Pagamenti': 'show',
      'Conto Economico': 'show'
    }
  },
  "file e documenti": {
    'status': 'show',
    'children': {
      'Atti e Contratti': 'show',
      'Certificazioni': 'show',
      'Verbali di cantiere': 'show',
      'Acquisti': 'show',
      'DDT': 'show',
      'Progettazione': 'show',
      'Impiantistica': 'show',
      'Planimetrie': 'show',
      'Render': 'show',
      'Tour virtuale': 'show',
      'Pratiche Edilizie': 'show',
      'Foto': 'show',
      'Video': 'show'
    }
  }
};

Map<String, Map<String, dynamic>> workMenuControls = {
  'progetti': {
    'status': 'show',
    'children': {
      'progetti-in-corso': 'show',
      'storico-progetti': 'show',
      'c-e-p': 'show'
    }
  },
  'ristrutturazioni': {
    'status': 'show',
    'children': {
      'segnalazioni-ristrutturazione': 'show',
      'contatti-ristrutturazione': 'show',
      'renovation-quotation': 'show',
      'renovation-project': 'show',
    }
  },
  'main-leads': {
    'status': 'show',
    'children': {
      'leads': 'show'
    }
  },
  'contatti': {
    'status': 'show',
    'children': {
      'contatti-ricevuti': 'show',
      'web-lead': 'show',
      'newarc-lead': 'show'
    }
  },
  'gestione': {
    'status': 'show',
    'children': {
      'agencies': 'show',
      'contractors': 'show',
      'team': 'show',
    }
  },
  'ads-manager': {
    'status': 'show',
    'children': {
      'active-ads': 'show',
      'archived-ads': 'show',
    }
  },
  'immagina': {
    'status': 'show',
    'children': {
      'progetti-attivi': 'show',
      'progetti-archiviati': 'show',
    }
  },
  'forniture': {
    'status': 'show',
    'children': {
      'pavimenti': 'show',
      'rivestimenti': 'show',
      'porte-interne': 'show',
      'sanitari': 'show',
      'illuminazione': 'show',
      'tinte': 'show'
    }
  },
  'materioteca': {
    'status': 'show',
    'children': {
      'moodboard': 'show'
    }
  }
};

Map<String, Map<String, dynamic>> allUserRoleMenuControls = {
  'master': various.deepCopy(workMenuControls),
  'finance': various.deepCopy(workMenuControls),
  'administration': various.deepCopy(workMenuControls),
  'media_creator': various.deepCopy(workMenuControls),
  'geometra': various.deepCopy(workMenuControls),
  'master-renderist': various.deepCopy(workMenuControls),
  'renderist': various.deepCopy(workMenuControls),
  'renovator': various.deepCopy(workMenuControls),
  'scouter': various.deepCopy(workMenuControls),
};

Map workMenuControlLabel = {
  'progetti': 'Operazioni Newarc',
  'progetti-in-corso': 'Progetti in corso',
  'storico-progetti': 'Storico progetti',
  'c-e-p': 'C.E. Previsionali',
  'ristrutturazioni': 'Ristrutturazioni',
  'main-leads': 'Gestisci Contatti',
  'leads': 'Gestisci Contatti',
  'segnalazioni-ristrutturazione': 'Segnalazioni ristrutturazione',
  'contatti-ristrutturazione': 'Clienti ristrutturazione',
  'renovation-quotation': 'Preventivi',
  'renovation-project': 'Progetti',
  'contatti': 'Contatti',
  'contatti-ricevuti': 'Valutazioni online',
  'web-client': 'Registrazioni sito',
  'web-lead': 'Lead acquisto',
  'newarc-lead': 'Lead configuratore',
  'gestione': 'Gestione',
  'agencies': 'Agenzie',
  'contractors': 'Ditte',
  'team': 'Persone',
  'ads-manager': 'Ads Manager',
  'active-ads': 'Annunci attivi',
  'archived-ads': 'Annunci archiviati',
  'immagina': 'Newarc Immagina',
  'progetti-attivi': 'Progetti attivi',
  'progetti-archiviati': 'Progetti archiviati',
  'forniture': 'Forniture',
  'pavimenti': 'Pavimenti',
  'rivestimenti': 'Rivestimenti',
  'tinte': 'Tinte',
  'porte-interne': 'Porte interne',
  'sanitari': 'Sanitari',
  'illuminazione': 'Illuminazione',
  'materioteca': 'Materioteca',
  'moodboard': 'Moodboard'
};

List<String> getFloorsList() {
  List<String> floorsList = List<String>.generate(15, (i) => (i + 1).toString());
  floorsList.insert(0, 'Piano rialzato');
  floorsList.insert(0, 'Piano terra');
  // floorsList.insert(0, 'Piano terra / rialzato');
  // floorsList.insert(0, '10+');
  
  return floorsList;
}

List<String> usageTypesList = ['Residenziale','Commerciale'];
