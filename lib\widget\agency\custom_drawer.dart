import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import '../../routes/agency_routes.dart';

class CustomDrawer extends StatefulWidget {
  CustomDrawer(
      {Key? key,this.agencyUser})
      : super(key: key);

  final AgencyUser? agencyUser;


  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  bool immaginaExpanded = false;
  ExpansibleController immaginaTile = new ExpansibleController();
  bool contattiTileExpand = false;
  ExpansibleController contattiTileKey = new ExpansibleController();
  bool reportTileExpand = false;
  ExpansibleController reportTileKey = new ExpansibleController();
  bool strumentiTileExpand = false;
  ExpansibleController strumentiTileKey = new ExpansibleController();
  bool strumentiAITileExpand = false;
  ExpansibleController strumentiAITileKey = new ExpansibleController();

  bool isDrawerOpen = true;

  String selectedView = 'progetti-attivi';

  void toggleDrawer() {
    setState(() {
      isDrawerOpen = !isDrawerOpen;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        AnimatedContainer(
          duration: Duration(milliseconds: 300),
          width: isDrawerOpen ? 257 : 80,
          color: Theme.of(context).primaryColorDark,
          child: Container(
            child: !isDrawerOpen
                ? Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 20, right: 20, top: 42),
                          child: Image.asset(
                            "assets/home-agencies.png",
                            height: 25,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: ListView(
                          shrinkWrap: true,
                          padding: EdgeInsets.all(0),
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 20, right: 20, top: 42),
                                child: Image.asset(
                                  'assets/logo-agenzie-white.png',
                                  height: 50,
                                ),
                              ),
                            ),
                            SizedBox(height: 40),
                            Theme(
                              data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('immagina'),
                                controller: immaginaTile,
                                initiallyExpanded: immaginaExpanded,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        immaginaExpanded = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                childrenPadding: EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(left: 15, right: 15, top: 5),
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_newarc.svg",
                                      height: 19,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  'Newarc Immagina',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  immaginaExpanded ? 'assets/icons/arrow_up.svg' : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'immagina-smart';
                                            context.go(AgencyRoutes.agencyImmagina("immagina-smart"));
                                            setState(() {});
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Immagina Smart',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('immagina-smart'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'progetti-attivi';
                                            setState(() {});
                                            context.go(AgencyRoutes.agencyImmagina('immagina-pro'));

                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Immagina Pro',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('progetti-attivi'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'progetti-archiviati';
                                            setState(() {});
                                            context.go(AgencyRoutes.agencyImmagina('progetti-archiviati'));
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Progetti archiviati',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('progetti-archiviati'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('strumenti-ai'),
                                controller: strumentiAITileKey,
                                initiallyExpanded: strumentiAITileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                    strumentiAITileExpand = value;
                                  },
                                )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ai.svg",
                                      height: 19,
                                      width: 21,
                                      alignment: Alignment.center,
                                      color: const Color.fromARGB(255, 129, 129, 129)
                                    )
                                  ),
                                ),
                                minTileHeight: 0,
                                childrenPadding:
                                EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                title: Text(
                                  'Strumenti AI',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  strumentiTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            context.go(AgencyRoutes.agencySmartDescription);
                                            selectedView = 'describe-ad';
                                            setState(() {});
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Descrivi immobile',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('describe-ad'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      //   SizedBox(
                                      //     height: 8,
                                      //  ),
                                      //  InkWell(
                                      //     onTap: () {
                                      //       widget.selectedView = 'rewrite-ad';
                                      //       widget.updateViewCallback!(widget.selectedView);
                                      //     },
                                      //     child: Row(
                                      //       children: [
                                      //         Text(
                                      //           'Riscrivi descrizione',
                                      //           style: TextStyle(
                                      //             fontSize: 14,
                                      //             fontFamily: 'Raleway-400',
                                      //             color: getColor('rewrite-ad'),
                                      //           ),
                                      //         ),
                                      //       ],
                                      //     ),
                                      //   ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('strumenti'),
                                controller: strumentiTileKey,
                                initiallyExpanded: strumentiTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                    strumentiTileExpand = value;
                                  },
                                )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_report.svg",
                                      height: 19,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                minTileHeight: 0,
                                childrenPadding:
                                EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                title: Text(
                                  'Newarc Reports',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  strumentiTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            context.go(AgencyRoutes.agencyReportAcquirente);
                                            selectedView = 'report-acquirente';
                                            setState(() {});
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Report Acquirente',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('report-acquirente'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('reporting'),
                                controller: reportTileKey,
                                initiallyExpanded: reportTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        reportTileExpand = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                childrenPadding:
                                    EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_segnala.svg",
                                      height: 19,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  'Segnala',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  reportTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'suggested-contacts';
                                            setState(() {});
                                            context.go(AgencyRoutes.agencySuggestedContacts);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Segnala ristrutturazione',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color:
                                                      getColor('suggested-contacts'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                key: Key('contatti'),
                                controller: contattiTileKey,
                                initiallyExpanded: contattiTileExpand,
                                onExpansionChanged: ((value) => setState(
                                      () {
                                        contattiTileExpand = value;
                                      },
                                    )),
                                collapsedIconColor: Colors.white,
                                iconColor: Colors.white,
                                minTileHeight: 0,
                                leading: SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/ic_contatti.svg",
                                      height: 19,
                                      width: 21,
                                      alignment: Alignment.center,
                                    ),
                                  ),
                                ),
                                childrenPadding:
                                    EdgeInsets.symmetric(horizontal: 15),
                                tilePadding: EdgeInsets.only(
                                    left: 15, right: 15, top: 5),
                                title: Text(
                                  'Contatti',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontFamily: 'semi-bold',
                                    color: Colors.white,
                                  ),
                                ),
                                trailing: SvgPicture.asset(
                                  contattiTileExpand
                                      ? 'assets/icons/arrow_up.svg'
                                      : 'assets/icons/arrow_down.svg',
                                  height: 6,
                                  width: 10,
                                  color: Colors.white,
                                ),
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 42),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'web-lead';
                                            setState(() {});
                                            context.go(AgencyRoutes.agencyWebLead);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Richieste visita',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color:
                                                      getColor('web-lead'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        InkWell(
                                          onTap: () {
                                            selectedView = 'contatti-ricevuti';
                                            setState(() {});
                                            context.go(AgencyRoutes.agencyContattiRicevuti);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                'Lead vendita',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontFamily: 'Raleway-400',
                                                  color: getColor('contatti-ricevuti'),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        Positioned(
          right: -10,
          top: 42,
          child: InkWell(
            onTap: toggleDrawer,
            child: Container(
              height: 28,
              width: 28,
              child: Center(
                child: SvgPicture.asset(
                  isDrawerOpen
                      ? "assets/icons/arrow_left.svg"
                      : "assets/icons/arrow_right.svg",
                  color: AppColor.drawerIconButtonColor,
                  height: 10,
                ),
              ),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                      offset: Offset(0, 10),
                      blurRadius: 10,
                      color: AppColor.black.withOpacity(0.1))
                ],
                color: AppColor.drawerButtonColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        )
      ],
    );
  }

  Color? getColor(String view) {
    if (selectedView == view) {
      return Theme.of(context).highlightColor;
    } else {
      return Colors.white;
    }
  }
}

enum ReceivedContactsPageFilters {
  vendiNewarc,
  vendiAgenzie,
  valutaCompra,
  valutaCuriosita,
  vendiProfessionista,
  valutaProfessionista
}
