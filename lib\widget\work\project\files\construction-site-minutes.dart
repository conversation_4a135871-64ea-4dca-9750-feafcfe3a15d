import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

import '../../../../utils/storage.dart';
import '../../../../utils/various.dart';
import '../../../UI/base_newarc_button.dart';
import '../../../UI/base_newarc_popup.dart';
import '../../../UI/custom_textformfield.dart';

class ProjectFileConstructionSiteMinutes extends StatefulWidget {
  final NewarcProject? project;
  final Function? updateProject;

  const ProjectFileConstructionSiteMinutes({Key? key, this.project,this.updateProject})
      : super(key: key);

  @override
  State<ProjectFileConstructionSiteMinutes> createState() => _ProjectFileConstructionSiteMinutesState();
}

class _ProjectFileConstructionSiteMinutesState extends State<ProjectFileConstructionSiteMinutes> {
  TextEditingController contDate = new TextEditingController();
  TextEditingController contTitle = new TextEditingController();

  int date = 0;
  final List<List> allFiles = [];


  List<String> fileProgressMessage = [''];
  String progressMessage = '';
  int keepOpen = -1;
  List<bool> isAnimated = [];

  @override
  void initState() {
    super.initState();

    setInitialValues();
  }

  @protected
  void didUpdateWidget(ProjectFileConstructionSiteMinutes oldWidget) {
    super.didUpdateWidget(oldWidget);

    setInitialValues();
  }

  setInitialValues() {
    allFiles.clear();
    isAnimated.clear();

    int counter = 0;
    widget.project?.constructionSiteMinutesFiles?.map((e) {
      allFiles.add(e.images!);
      isAnimated.add(false);

      if (keepOpen > -1 && counter == keepOpen) {
        isAnimated[keepOpen] = true;
      }

      counter++;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: ListView(
              children: [
                SizedBox(height: 20),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      flex: 1,
                      child: NarFormLabelWidget(
                        label: 'Verbali di cantiere',
                        fontSize: 20,
                        fontWeight: 'bold',

                      ),
                    ),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: BaseNewarcButton(
                        onPressed: () {
                          addVerbaliPopup(context);
                        },
                        buttonText: 'Nuovo verbale',
                      ),
                    )
                  ],
                ),
                SizedBox(height: 30),
                Container(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      ...?widget.project?.constructionSiteMinutesFiles?.reversed.map((e) {
                        return verbaliWrapper(context, e);
                      }).toList()
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future saveProject() async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        .doc(widget.project!.id)
        .update(widget.project!.toMap());

    widget.updateProject!(widget.project);
    setInitialValues();
  }

  Widget verbaliWrapper(BuildContext context, ConstructionSiteMinutesFiles row) {
    int count = widget.project?.constructionSiteMinutesFiles
        ?.indexWhere((e) => e.uid == row.uid) ?? 0;
    progressMessage = '';

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Color(0xffF1F1F1),
            borderRadius: BorderRadius.circular(10),
          ),
          margin: EdgeInsets.only(bottom: 5),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: row.title ?? "",
                            fontSize: 16,
                            textColor: Colors.black,
                            fontWeight: 'bold',
                          ),
                          Row(
                            children: [
                              NarFormLabelWidget(
                                label: timestampToUtcDate(row.date!),
                                fontSize: 16,
                                textColor: Colors.black,
                                fontWeight: 'bold',
                              ),
                              const SizedBox(width: 20),

                              isAnimated[count]
                                  ? GestureDetector(
                                onTap: () {
                                  deleteDialog(context, row);
                                },
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Image.asset(
                                    'assets/icons/trash-process.png',
                                    height: 16,
                                    color: Color(0xFF5B5B5B),
                                  ),
                                ),
                              )
                                  : GestureDetector(
                                onTap: () {
                                  setState(() {
                                    isAnimated[count] = true;
                                    keepOpen = count;
                                  });
                                },
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Image.asset(
                                    'assets/icons/arrow_down.png',
                                    width: 14,
                                    color: Color(0xFF5B5B5B),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    isAnimated[count]
                        ? Padding(
                      padding: const EdgeInsets.only(
                          bottom: 0, top: 8.0, left: 8, right: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 10),
                            constraints: const BoxConstraints(
                              minHeight: 100,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          SizedBox(
                                            width: 120,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              displayFormat: 'inline-button',
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica verbali',
                                              borderSideColor:
                                              Theme.of(context).primaryColor,
                                              hoverColor: const Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: allFiles[count],
                                              pageContext: context,
                                              storageDirectory:
                                              "projects/${widget.project!.id}/construction-site-minutes/${row.uid}/",
                                              onUploadCompleted: () => onUploadCompleted(row.uid!, count),
                                              progressMessage: [""],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 30),
                                      SizedBox(
                                        width: 350,
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          itemCount: 1,
                                          itemBuilder: (context, index) {
                                            return NarFilePickerWidget(
                                              allowMultiple: false,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: true,
                                              removeButtonText: 'Elimina',
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText:
                                              '+ espandi',
                                              actionButtonPosition:
                                              'bottom',
                                              displayFormat:
                                              'inline-widget',
                                              containerWidth: 110,
                                              containerHeight: 110,
                                              containerBorderRadius: 13,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica verbali',
                                              borderSideColor:
                                              Theme.of(context)
                                                  .primaryColor,
                                              hoverColor:
                                              const Color.fromRGBO(
                                                  133, 133, 133, 1),
                                              allFiles: allFiles[count],
                                              pageContext: context,
                                              storageDirectory:
                                              "projects/${widget.project!.id}/construction-site-minutes/${row.uid}/",
                                              removeExistingOnChange: true,
                                              onUploadCompleted:(files) async {
                                                setState(() {});
                                              },
                                              progressMessage: [""],
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                        : Container(),

                    isAnimated[count]
                        ? Padding(
                      padding: const EdgeInsets.only(
                          top: 0, bottom: 8.0, right: 8, left: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isAnimated[count] = false;
                                keepOpen = -1;
                              });
                            },
                            child: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: Image.asset(
                                'assets/icons/arrow_up.png',
                                width: 14,
                                color: Color(0xFF5B5B5B),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                        : Container(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }


  onUploadCompleted (String rowUid,int count) async {
    try {
      ConstructionSiteMinutesFiles? _constructionSiteMinutesFiles =
      widget.project!.constructionSiteMinutesFiles!
          .firstWhere((e) => rowUid == e.uid);

      _constructionSiteMinutesFiles.images = allFiles[count];

      log("allFiles[count] ==> ${allFiles[count]}");
      log("_constructionSiteMinutesFiles.images ==> ${_constructionSiteMinutesFiles.images}");

      final FirebaseFirestore _db = FirebaseFirestore.instance;
      await _db
          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
          .doc(widget.project!.id)
          .update(widget.project!.toMap());

      widget.updateProject!(widget.project);

      setState(() {});

    } catch (e, s) {
      debugPrint("Error while saving images: $e");
      debugPrint("$s");
    }
  }
  
  deleteVerbaliFiles(ConstructionSiteMinutesFiles row) async {
    try {
      await deleteDirectory("projects/${widget.project!.id}/construction-site-minutes/${row.uid}/");
    } catch(e) {

    }

  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  deleteDialog(BuildContext context, ConstructionSiteMinutesFiles row) {
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {
                return Center(
                    child: BaseNewarcPopup(
                      title: 'Rimuovi verbali',
                      buttonText: 'Rimuovi',
                      onPressed: () async {

                        await deleteVerbaliFiles(row);

                        widget.project!.constructionSiteMinutesFiles!.removeWhere((e) =>  e.uid == row.uid  );
                        await saveProject();

                        setInitialValues();
                        setState(() {

                        });
                        return true;
                      },
                      column: Container(
                          height: 99,
                          width: 465,
                          child: Center(
                            child: NarFormLabelWidget(
                                overflow: TextOverflow.visible,
                                label: 'Vuoi davvero eliminare questa verbali?',
                                textAlign: TextAlign.center,
                                fontSize: 18,
                                fontWeight: '600',
                                height: 1.5,
                                textColor: Color(0xFF696969)),
                          )),
                    ));
              });
        });
  }

  addVerbaliPopup(BuildContext context) async {
    contDate.text = '';
    contTitle.text = '';
    return showDialog(
        context: context,
        builder: (BuildContext _bc1) {
          return StatefulBuilder(
              builder: (BuildContext _bc2, StateSetter _setState) {
                return Center(
                    child: BaseNewarcPopup(
                      title: 'Nuovo verbali',
                      buttonText: 'Aggiungi',
                      onPressed: () async {
                        _setState(() {
                          progressMessage = 'Salvataggio in corso...';
                        });
                        try {
                          ConstructionSiteMinutesFiles _val = ConstructionSiteMinutesFiles.empty();
                          _val.indexPlace = (widget.project?.constructionSiteMinutesFiles?.length ?? 0) + 1;
                          _val.title = contTitle.text;
                          _val.date = date;
                          _val.uid = generateRandomString(8);
                          _val.images = [];


                          widget.project?.constructionSiteMinutesFiles?.add(_val);

                          date = 0;

                          final FirebaseFirestore _db = FirebaseFirestore.instance;
                          await _db
                              .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                              .doc(widget.project!.id)
                              .update(widget.project!.toMap());

                          widget.updateProject!(widget.project);
                          setInitialValues();

                          _setState(() {
                            progressMessage = 'Saved!';
                          });
                          setState(() {});

                        } catch (e, s) {
                          print({e, s});
                        }

                        setInitialValues();

                        return true;
                      },
                      column: Container(
                        height: 190,
                        width: 480,
                        padding: EdgeInsets.only(left: 40, right: 40),
                        child: ListView(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                              label: "Nome verbali",
                                              controller: contTitle,
                                              validator: (value) {
                                                if (value == '') {
                                                  return 'Required!';
                                                }

                                                return null;
                                              }
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10,),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Data cantiere",
                                            controller: contDate,

                                            suffixIcon: Container(
                                              padding: const EdgeInsets.all(10),
                                              height: 20,
                                              width: 20,
                                              child: Image.asset('assets/icons/calendar.png'),
                                            ),
                                            // validationMessage: 'Required!',
                                            validator: (value) {
                                              if (value == '') {
                                                return 'Required!';
                                              }

                                              return null;
                                            },
                                            onTap: () async {
                                              DateTime? pickedDate = await showDatePicker(
                                                  context: context,
                                                  initialDate: contDate.text == ''
                                                      ? DateTime.now()
                                                      : DateTime.tryParse(formatDateForParsing(contDate.text))!,
                                                  firstDate: DateTime(1950),
                                                  lastDate: DateTime(2300));

                                              if (pickedDate != null) {
                                                date = pickedDate.millisecondsSinceEpoch;
                                                String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);

                                                setState(() {
                                                  contDate.text = formattedDate;
                                                });
                                              }
                                            },
                                            onChangedCallback: (value) async {},
                                          ),
                                        ],
                                      ),

                                    ],
                                  ),
                                ),
                              ],
                            ),

                            SizedBox(
                              height: 5,
                            ),
                            NarFormLabelWidget(
                              label: progressMessage,
                              textAlign: TextAlign.center,
                              textColor: Color(0xff696969),
                              fontSize: 13,
                              fontWeight: '600',
                            ),
                          ],
                        ),
                      ),
                    ));
              });
        });
  }

}
