import "package:cloud_firestore/cloud_firestore.dart";
import "package:newarc_platform/classes/baseAddressInfo.dart";


class PropertyAdditionalCharacteristics {
  bool? elevator;
  bool? hasCantina;
  bool? airConditioning;
  bool? securityDoor;
  bool? fiber;
  bool? tvStation;
  bool? highEfficiencyFrames;
  bool? alarm;
  bool? terrace;
  bool? sharedGarden;
  bool? privateGarden;
  bool? hasConcierge;
  bool? motorizedSunblind;
  bool? domotizedSunblind;
  bool? domotizedLights;
  bool? centralizedHeating;
  bool? autonomousHeating;
  bool? highFloor;
  bool? metroVicinity;
  bool? bigBalconies;
  bool? doubleEsposition;
  bool? tripleEsposition;
  bool? quadrupleEsposition;
  bool? bigLiving;
  bool? doubleBathroom;
  bool? nobleBuilding;
  bool? surveiledBuilding;
  bool? swimmingPool;
  bool? hasGarage;
  String? energyClass;
  int? constructionYear;
  late List externalEsposition;
  bool? solarPanel;
  bool? walkInCloset;

  PropertyAdditionalCharacteristics.empty() {
    this.elevator = null;
    this.hasCantina = null;
    this.airConditioning = null;
    this.securityDoor = null;
    this.fiber = null;
    this.tvStation = null;
    this.highEfficiencyFrames = null;
    this.alarm = null;
    this.terrace = null;
    this.sharedGarden = null;
    this.privateGarden = null;
    this.hasConcierge = null;
    this.motorizedSunblind = null;
    this.domotizedSunblind = null;
    this.domotizedLights = null;
    this.centralizedHeating = null;
    this.autonomousHeating = null;
    this.highFloor = null;
    this.metroVicinity = null;
    this.bigBalconies = null;
    this.doubleEsposition = null;
    this.tripleEsposition = null;
    this.quadrupleEsposition = null;
    this.bigLiving = null;
    this.doubleBathroom = null;
    this.nobleBuilding = null;
    this.surveiledBuilding = null;
    this.swimmingPool = null;
    this.hasGarage = null;
    this.energyClass = null;
    this.constructionYear = null;
    this.externalEsposition = [];
    this.solarPanel = null;
    this.walkInCloset = null;
  }

  PropertyAdditionalCharacteristics.fromDocument(Map<String, dynamic> data) {
    this.elevator = data['elevator'];
    this.hasCantina = data['hasCantina'];
    this.airConditioning = data['airConditioning'];
    this.securityDoor = data['securityDoor'];
    this.fiber = data['fiber']; 
    this.tvStation = data['tvStation'];
    this.highEfficiencyFrames = data['highEfficiencyFrames'];
    this.alarm = data['alarm'];
    this.terrace = data['terrace'];
    this.sharedGarden = data['sharedGarden'];
    this.privateGarden = data['privateGarden'];
    this.hasConcierge = data['hasConcierge'];
    this.motorizedSunblind = data['motorizedSunblind'];
    this.domotizedSunblind = data['domotizedSunblind'];
    this.domotizedLights = data['domotizedLights'];
    this.centralizedHeating = data['centralizedHeating'];
    this.autonomousHeating = data['autonomousHeating'];
    this.highFloor = data['highFloor'];
    this.metroVicinity = data['metroVicinity'];
    this.bigBalconies = data['bigBalconies'];
    this.doubleEsposition = data['doubleEsposition'];
    this.tripleEsposition = data['tripleEsposition'];
    this.quadrupleEsposition = data['quadrupleEsposition'];
    this.bigLiving = data['bigLiving'];
    this.doubleBathroom = data['doubleBathroom'];
    this.nobleBuilding = data['nobleBuilding'];
    this.surveiledBuilding = data['surveiledBuilding'];
    this.swimmingPool = data['swimmingPool'];
    this.hasGarage = data['hasGarage'];
    this.energyClass = data['energyClass'];
    this.constructionYear = data['constructionYear'];
    this.externalEsposition = data['externalEsposition'] ?? [];
    this.solarPanel = data['solarPanel'];
    this.walkInCloset = data['walkInCloset'];
  }

  Map<String, dynamic> toMap() {
    return {
      'elevator': this.elevator,
      'hasCantina': this.hasCantina,
      'airConditioning': this.airConditioning,
      'securityDoor': this.securityDoor,
      'fiber': this.fiber,
      'tvStation': this.tvStation,
      'highEfficiencyFrames': this.highEfficiencyFrames,
      'alarm': this.alarm,
      'terrace': this.terrace,
      'sharedGarden': this.sharedGarden,
      'privateGarden': this.privateGarden,
      'hasConcierge': this.hasConcierge,
      'motorizedSunblind': this.motorizedSunblind,
      'domotizedSunblind': this.domotizedSunblind,
      'domotizedLights': this.domotizedLights,
      'centralizedHeating': this.centralizedHeating,
      'autonomousHeating': this.autonomousHeating,
      'highFloor': this.highFloor,
      'metroVicinity': this.metroVicinity,
      'bigBalconies': this.bigBalconies,
      'doubleEsposition': this.doubleEsposition,
      'tripleEsposition': this.tripleEsposition,
      'quadrupleEsposition': this.quadrupleEsposition,
      'bigLiving': this.bigLiving,
      'doubleBathroom': this.doubleBathroom,
      'nobleBuilding': this.nobleBuilding,
      'surveiledBuilding': this.surveiledBuilding,
      'swimmingPool': this.swimmingPool,
      'hasGarage': this.hasGarage,
      'energyClass': this.energyClass,
      'constructionYear': this.constructionYear,
      'externalEsposition': this.externalEsposition,
      'solarPanel': this.solarPanel,
      'walkInCloset': this.walkInCloset,
    };
  }

  void updateFromCharacteristicsMapITA(Map<String, dynamic> data) {
    this.elevator = data['Ascensore'];
    this.hasCantina = data['Cantina'];
    this.airConditioning = data['Pred. condizionatore'];
    this.securityDoor = data['Porta blindata'];
    this.fiber = data['Fibra ottica'];
    this.tvStation = data['Impianto TV'];
    this.highEfficiencyFrames = data['Infissi ad alta efficienza'];
    this.alarm = data['Pred. antifurto'];
    this.terrace = data['Terrazzo'];
    this.sharedGarden = data['Giardino condominiale'];
    this.privateGarden = data['Giardino privato'];
    this.hasConcierge = data['Portineria'];
    this.motorizedSunblind = data['Tapparelle motorizzate'];
    this.domotizedSunblind = data['Tapparelle domotizzate'];
    this.domotizedLights = data['Luci domotizzate'];
    this.centralizedHeating = data['Risc. centralizzato'];
    this.autonomousHeating = data['Risc. autonomo'];
    this.highFloor = data['Piano alto'];
    this.metroVicinity = data['Vicinanza Metro'];
    this.bigBalconies = data['Ampi balconi'];
    this.doubleEsposition = data['Doppia esposizione'];
    this.tripleEsposition = data['Tripla esposizione'];
    this.quadrupleEsposition = data['Quadrupla esposizione'];
    this.bigLiving = data['Grande zona living'];
    this.doubleBathroom = data['Doppi servizi'];
    this.nobleBuilding = data['Stabile signorile'];
    this.surveiledBuilding = data['Stabile videosorvegliato'];
    this.swimmingPool = data['Piscina'];
    this.hasGarage = data['Box o garage'];
    this.solarPanel = data['Fotovoltaico'];
    this.walkInCloset = data['Cabina armadio'];
  }

  Map<String, dynamic> toCharacteristicsMapITA() {
    return {
      'Ascensore': this.elevator ?? false,
      'Cantina': this.hasCantina ?? false,
      'Pred. condizionatore': this.airConditioning ?? false,
      'Porta blindata': this.securityDoor ?? false,
      'Fibra ottica': this.fiber ?? false,
      'Impianto TV': this.tvStation ?? false,
      'Infissi ad alta efficienza': this.highEfficiencyFrames ?? false,
      'Pred. antifurto': this.alarm ?? false,
      'Terrazzo': this.terrace ?? false,
      'Giardino condominiale': this.sharedGarden ?? false,
      'Giardino privato': this.privateGarden ?? false,
      'Portineria': this.hasConcierge ?? false,
      'Tapparelle motorizzate': this.motorizedSunblind ?? false,
      'Tapparelle domotizzate': this.domotizedSunblind ?? false,
      'Luci domotizzate': this.domotizedLights ?? false,
      'Risc. centralizzato': this.centralizedHeating ?? false,
      'Risc. autonomo': this.autonomousHeating ?? false,
      'Piano alto': this.highFloor ?? false,
      'Vicinanza Metro': this.metroVicinity ?? false,
      'Ampi balconi': this.bigBalconies ?? false,
      'Doppia esposizione': this.doubleEsposition ?? false,
      'Tripla esposizione': this.tripleEsposition ?? false,
      'Quadrupla esposizione': this.quadrupleEsposition ?? false,
      'Grande zona living': this.bigLiving ?? false,
      'Doppi servizi': this.doubleBathroom ?? false,
      'Stabile signorile': this.nobleBuilding ?? false,
      'Stabile videosorvegliato': this.surveiledBuilding ?? false,
      'Piscina': this.swimmingPool ?? false,
      'Box o garage': this.hasGarage ?? false,
      'Fotovoltaico': this.solarPanel ?? false,
      'Cabina armadio': this.walkInCloset ?? false,
    };
  }
}


class SmartAdDescription extends PropertyAdditionalCharacteristics{
  String? id;
  String? addressId;
  String? agencyId;
  String? userId;
  int? insertTimestamp;
  late bool isArchived;
  String? acquirerType;
  String? usageType;
  String? contractType;
  String? propertyType;
  int? grossSquareFootage;
  int? rooms;
  int? numberOfBathrooms;
  String? unitFloor;
  String? buildingFloorCount;
  String? maintenanceStatus;
  String? additionalInfo;
  late GeneratedAdDescription generation;
  // only for UI (not stored in db, populated on fetch address)
  BaseAddressInfo? addressInfo;
  
  SmartAdDescription.empty() : super.empty() {
    this.id = null;
    this.addressId = null;
    this.agencyId = null;
    this.userId = null;
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.isArchived = false;
    this.acquirerType = null; // either "Privato" or "Investitore" or "Famiglia" or "Giovani" or "Azienda"
    this.usageType = null; // either "Residenziale" or "Commerciale"
    this.contractType = null; // either "Affitto" or "Vendita"
    this.propertyType = null; // bil, trilo, attico
    this.grossSquareFootage = null;
    this.rooms = null;
    this.numberOfBathrooms = null;
    this.unitFloor = null;
    this.buildingFloorCount = null;
    this.maintenanceStatus = null;
    this.additionalInfo = null;
    this.generation = GeneratedAdDescription.empty();
  }

  SmartAdDescription.fromDocument(Map<String, dynamic> data, String id) : super.fromDocument(data) {
    this.id = id;
    this.addressId = data['addressId'];
    this.agencyId = data['agencyId'];
    this.userId = data['userId'];
    this.insertTimestamp = data['insertTimestamp'];
    this.isArchived = data['isArchived'] ?? false;
    this.acquirerType = data['acquirerType'];
    this.usageType = data['usageType'];
    this.contractType = data['contractType'];
    this.propertyType = data['propertyType'];
    this.grossSquareFootage = data['grossSquareFootage'];
    this.rooms = data['rooms'];
    this.numberOfBathrooms = data['numberOfBathrooms'];
    this.unitFloor = data['unitFloor'];
    this.buildingFloorCount = data['buildingFloorCount'];
    this.maintenanceStatus = data['maintenanceStatus'];
    this.additionalInfo = data['additionalInfo'];
    this.generation = data['generation'] != null ? GeneratedAdDescription.fromDocument(data['generation']) : GeneratedAdDescription.empty();
  }


  Map<String, dynamic> toMap() {
    Map<String, dynamic> baseMap = super.toMap();
    baseMap.addAll({
      'id': this.id,
      'addressId': this.addressId,
      'agencyId': this.agencyId,
      'userId': this.userId,
      'insertTimestamp': this.insertTimestamp,
      'isArchived': this.isArchived,
      'acquirerType': this.acquirerType,
      'usageType': this.usageType,
      'contractType': this.contractType,
      'propertyType': this.propertyType,
      'grossSquareFootage': this.grossSquareFootage,
      'rooms': this.rooms,
      'numberOfBathrooms': this.numberOfBathrooms,
      'unitFloor': this.unitFloor,
      'buildingFloorCount': this.buildingFloorCount,
      'maintenanceStatus': this.maintenanceStatus,
      'additionalInfo': this.additionalInfo,
      'generation': this.generation.toMap(),
    });
    return baseMap;
  }
}

class GeneratedAdDescription {
  bool? generationStatus;
  String? generatedDescription;
  String? generationError;
  double? generationTimeS;
  Map? generationTokenUsage;
  bool? generationGrade;
  String? generationLanguage;
  double? generationCharactersLenght;
  late int insertTimestamp;

  GeneratedAdDescription.empty() {
    this.generationStatus = null;
    this.generatedDescription = null;
    this.generationError = null;
    this.generationTimeS = null;
    this.generationTokenUsage = null;
    this.generationGrade = null;
    this.generationLanguage = null;
    this.generationCharactersLenght = null;
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
  }

  GeneratedAdDescription.fromDocument(Map<String, dynamic> data) {
    this.generationStatus = data['generationStatus'];
    this.generatedDescription = data['generatedDescription'];
    this.generationError = data['generationError'];
    this.generationTimeS = data['generationTimeS'];
    this.generationTokenUsage = data['generationTokenUsage'];
    this.generationGrade = data['generationGrade'];
    this.generationLanguage = data['generationLanguage'];
    this.generationCharactersLenght = data['generationCharactersLenght'];
    this.insertTimestamp = data['insertTimestamp'];
  }

  Map<String, dynamic> toMap() {
    return {
      'generationStatus': this.generationStatus,
      'generatedDescription': this.generatedDescription,
      'generationError': this.generationError,
      'generationTimeS': this.generationTimeS,
      'generationTokenUsage': this.generationTokenUsage,
      'generationGrade': this.generationGrade,
      'generationLanguage': this.generationLanguage,
      'generationCharactersLenght': this.generationCharactersLenght,
      'insertTimestamp': this.insertTimestamp,
    };
  }
}
