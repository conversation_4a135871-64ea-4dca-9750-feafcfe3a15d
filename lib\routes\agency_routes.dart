import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/pages/agency/acquired_agency_view.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/pages/agency/agency_immagina/agency_immagina_view.dart';
import 'package:newarc_platform/pages/agency/agency_immagina_archive/agency_immagina_project_archive_view.dart';
import 'package:newarc_platform/pages/agency/agency_persone/agency_persone_view.dart';
import 'package:newarc_platform/pages/agency/buyer_report/buyer_report_inside_view.dart';
import 'package:newarc_platform/pages/agency/buyer_report/buyer_report_view.dart';
import 'package:newarc_platform/pages/agency/inside_project_view.dart';
import 'package:newarc_platform/pages/agency/inside_request_view.dart';
import 'package:newarc_platform/pages/agency/newarc_properties_view.dart';
import 'package:newarc_platform/pages/agency/operations_view.dart';
import 'package:newarc_platform/pages/agency/prices_sold.dart';
import 'package:newarc_platform/pages/agency/proposed_estates.dart';
import 'package:newarc_platform/pages/agency/settings.dart';
import 'package:newarc_platform/pages/agency/suggested_contacts/suggested_contacts_view.dart';
import 'package:newarc_platform/pages/agency/ai_instruments/ad_description_view.dart';
import 'package:newarc_platform/pages/agency/ai_instruments/ad_description_inside_view.dart';
import 'package:newarc_platform/pages/common/web_leads/web_leads_view.dart';
import 'package:newarc_platform/routes/user_provider.dart';
import 'package:newarc_platform/widget/agency/abbonamento_view.dart';
import 'package:newarc_platform/widget/agency/services_view.dart';
import 'package:newarc_platform/pages/agency/home_agency.dart';
import 'package:provider/provider.dart';


class AgencyRoutes {
  static const String agencyAgenzieIscritte = '/agenzie-iscritte';
  static const String agencyOperazioniNewarc = '/operazioni-newarc';
  static const String agencyProponiImmobile = '/proponi-immobile';
  static const String agencyPrezziVenduto = '/prezzi-venduto';
  static const String agencyNewarcProperties = '/newarc-properties';
  static const String agencyAgencySetting = '/agency-setting';
  static const String agencyAbbonamento = '/abbonamento';
  static const String agencyContattiRicevuti = '/contatti/lead-vendita';
  static const String agencyWebLead = '/contatti/contatti-ricevuti';
  static const String agencySuggestedContacts = '/segnala/segnala-ristrutturazione';
  static const String agencyAgencyPersona = '/agency-persona';
  static const String agencyServiziPrezzi = '/servizi-prezzi';
  static const String agencyReportAcquirente = '/report/report-acquirente';
  static const String agencySmartDescription = '/strumenti-ai/descrivi-annuncio';

  static String agencyReportAcquirenteId(String id) => '/report/report-acquirente/$id';
  static String agencyImmagina(String mode) => '/immagina/$mode';
  static String agencyImmaginaInside({
    required String mode,
    required String id,
    required String childMode,
  }) => '/immagina/$mode/$childMode/$id';
  static String agencySmartDescriptionId(String id) => '/strumenti-ai/descrivi-annuncio/$id';

  static final routes = [
    ShellRoute(
      pageBuilder: (context, state, child) {
        final user = context.read<UserProvider>().agencyUser;

        if (user == null) {
          return NoTransitionPage(
            child: Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        return NoTransitionPage(
          child: HomeAgency(
            agencyUser: user,
            child: child,
          ),
        );
      },
      routes: [
        GoRoute(
          path: agencyAgenzieIscritte,
          name: 'agenzie-iscritte',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(child: AcquiredAgencyView(agency: user.agency!));
          },
        ),
        GoRoute(
          path: agencyContattiRicevuti,
          name: 'contatti-ricevuti',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AcquiredContactsView(
                agency: user.agency!,
                agencyUser: user,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyOperazioniNewarc,
          name: 'operazioni-newarc',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: OperationsView(
                agency: user.agency!,
                agencyUser: user,
                responsive: true,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyProponiImmobile,
          name: 'proponi-immobile',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: ProposedEstatesView(
                agency: user.agency!,
                agencyUser: user,
                responsive: true,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyPrezziVenduto,
          name: 'prezzi-venduto',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: PricesSoldView(
                agency: user.agency!,
                agencyUser: user,
                responsive: true,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyNewarcProperties,
          name: 'newarc-properties',
          pageBuilder: (context, state) => NoTransitionPage(child: NewarcPropertiesView()),
        ),
        GoRoute(
          path: agencyAgencySetting,
          name: 'agency-setting',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AgencySetting(
                agency: user.agency!,
                agencyUser: user,
              ),
            );
          },
        ),
        GoRoute(
          path: '/immagina/:mode',
          name: 'immagina-view',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            final parentMode = state.pathParameters['mode']!;
            final isSmartView = parentMode == 'immagina-smart';
            final isArchived = parentMode == 'progetti-archiviati';

            if (isArchived) {
              return NoTransitionPage(
                child: AgencyImmaginaProjectArchiveView(
                  agencyUser: user,
                ),
              );
            } else {
              return NoTransitionPage(
                child: AgencyImmaginaView(
                  key: ValueKey("immagina-$parentMode"),
                  agencyUser: user,
                  isSmart: isSmartView,
                ),
              );
            }
          },
          routes: [
            GoRoute(
              path: ':childMode/:id',
              name: 'immagina-inside',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().agencyUser!;
                final id = state.pathParameters['id']!;
                final mode = state.pathParameters['childMode']!;
                final parentMode = state.pathParameters['mode']!;
                final isSmartView = parentMode == 'immagina-smart';

                if (mode == "request") {
                  return NoTransitionPage(
                    child: InsideRequestView(
                      projectFirebaseId: id,
                      agencyUser: user,
                      isFromRequest: true,
                      isSmart: isSmartView,
                      isForProfessionals: false,
                    ),
                  );
                } else {
                  return NoTransitionPage(
                    child: InsideProjectView(
                      projectFirebaseId: id,
                      agencyUser: user,
                      isFromRequest: false,
                      isFromProjectArchive: false,
                    ),
                  );
                }
              },
            ),
          ],
        ),
        GoRoute(
          path: agencyAbbonamento,
          name: 'abbonamento',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AbbonamentoView(
                agencyUser: user.agency!,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyWebLead,
          name: 'web-lead',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: WebLeadsView(
                agencyId: user.agencyId,
              ),
            );
          },
        ),
        GoRoute(
          path: agencySuggestedContacts,
          name: 'suggested-contacts',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: SuggestedContactsView(
                agencyId: user.agencyId,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyAgencyPersona,
          name: 'agency-persona',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AgencyPersoneView(
                agencyId: user.agencyId!,
              ),
            );
          },
        ),
        GoRoute(
          path: agencyServiziPrezzi,
          name: 'servizi-prezzi',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AgencyServiziView(
                agencyUser: user.agency!,
              ),
            );
          },
        ),
        GoRoute(
          path: agencySmartDescription,
          name: 'describe-ad',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: AdDescriptionView(
                key: Key('describe-ad'),
                agencyUser: user,
              ),
            );
          },
          routes: [
            GoRoute(
              path: ':id',
              name: 'inside-describe-ad',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().agencyUser!;
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: InsideAdDescriptionView(
                    key: Key('inside-describe-ad-$id'),
                    agencyUser: user,
                    smartAdDescriptionId: id,
                  ),
                );
              },
            ),
          ],
        ),
        GoRoute(
          path: agencyReportAcquirente,
          name: 'report-acquirente',
          pageBuilder: (context, state) {
            final user = context.read<UserProvider>().agencyUser!;
            return NoTransitionPage(
              child: BuyerReportView(
                agencyUser: user,
              ),
            );
          },
          routes: [
            GoRoute(
              path: ':id',
              name: 'report-acquirente-inside',
              pageBuilder: (context, state) {
                final user = context.read<UserProvider>().agencyUser!;
                final id = state.pathParameters['id']!;
                return NoTransitionPage(
                  child: BuyerReportInsideView(
                    agencyUser: user,
                    projectFirebaseId: id,
                  ),
                );
              },
            ),
          ],
        ),
      ],
    ),
  ];
}
