import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/register_page.dart';
import 'package:newarc_platform/routes/professional_routes.dart';
import 'package:newarc_platform/routes/user_provider.dart';
import 'package:newarc_platform/routes/work_routes.dart';
import 'package:provider/provider.dart';
import '../pages/404_page.dart';
import '../pages/successful_payment_page.dart';
import '../pages/user_management.dart';
import 'agency_routes.dart';


class AppRouter {

  static GoRouter createRouter(String accessType) {
    return GoRouter(
      errorBuilder: (context, state) => Page404(accessType: accessType,),
      debugLogDiagnostics: true,
      restorationScopeId: 'app-router',
      initialLocation: "/",
      redirect: (context, state) {
        final userProvider = context.read<UserProvider>();
        final loggedIn = userProvider.isLoggedIn;
        final path = state.uri.toString();
        final loggingIn = path.startsWith('/login');
        final registering = path.endsWith('/register');
        final successPayment = path.startsWith('/successfulPayment');
        final userMgmt = path.startsWith('/usermgmt');

        if (userProvider.isLoading) return null;

        if (!loggedIn && !loggingIn && !registering && !successPayment && !userMgmt) {
          return '/login/$accessType';
        }

        if (loggedIn && (loggingIn || registering)) {
          final from = state.uri.queryParameters['from'];
          if (from != null && from.isNotEmpty) {
            return Uri.decodeComponent(from);
          }
          if (userProvider.agencyUser?.agencyId != null) {
            return AgencyRoutes.agencyImmagina('immagina-pro');
          }
          if (userProvider.workUser?.id != null && userProvider.workUser?.id != "") return WorkRoutes.workRenovationProject;
          // if (userProvider.supplierUser?.id != null && userProvider.supplierUser?.id != "") return '/home-supplier/dashboard';
          if (userProvider.professionalsUser?.id != null && userProvider.professionalsUser?.id != "") return ProfessionalRoutes.professionalImmagina("progetti-attivi");
        }

        return null;
      },
      routes: [
        GoRoute(
          path: '/',
          redirect: (context, state) => '/login/$accessType',
        ),
        // ----> Login
        GoRoute(
          path: '/login/:type',
          pageBuilder: (context, state) {
            final loginType = state.pathParameters['type']!;
            return NoTransitionPage(child: LoginPage(loginType: loginType,));
          },
        ),

        // ----> Register
        GoRoute(
          path: '/register/:type',
          pageBuilder: (context, state) {
            final origin = state.pathParameters['type']!;
            return NoTransitionPage(child: RegisterPage(origin: origin));
          },
        ),

        // ----> successfulPayment
        GoRoute(
          path: '/successfulPayment',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: SuccessfulPaymentPage());
          },
        ),

        // ----> successfulPayment
        GoRoute(
          path: '/usermgmt',
          pageBuilder: (context, state) {
            return NoTransitionPage(child: UserManagementPage());
          },
        ),

        if(accessType == "agency")
        ...AgencyRoutes.routes,

        if(accessType == "professionals")
        ...ProfessionalRoutes.routes,

        if(accessType == "work")
        ...WorkRoutes.routes,

      ],
    );
  }
}
